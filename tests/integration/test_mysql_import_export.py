#!/usr/bin/env python3

"""
MySQL 连接器集成测试。

本模块提供了完整的MySQL数据导入导出功能集成测试，包括：
1. 基础导入导出测试
2. 数据类型兼容性测试
3. 时区处理专项测试
4. 分批导出专项测试
5. 主键冲突处理专项测试

测试环境：
- 使用Docker容器启动MySQL和MinIO服务
- 配置测试用的S3存储桶和数据库连接
- 确保容器在测试开始前完全启动并可用

测试策略：
- 每个测试方法专注一个特定场景
- 为不同测试场景创建独立的测试表，避免测试用例间的数据污染
- 使用pytest框架编写测试用例
- 实现适当的测试数据准备和清理逻辑
- 添加详细的断言验证，确保数据准确性
- 包含异常场景的测试覆盖
"""

import pandas as pd
import pytest
from faker import Faker
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncEngine

from datax.connectors.mysql import MySQLExtractor, MySQLLoader, MySQLMetadataProvider
from datax.models import (
    ConflictStrategy,
    ExportConfig,
    ImportConfig,
    MySQLConfig,
    QueryConfig,
    S3Config,
)

# 初始化faker，使用中文区域设置
fake = Faker(["zh_CN", "en_US"])


@pytest.fixture
def test_s3_config(s3_config: S3Config) -> S3Config:
    """测试用的S3配置"""
    return s3_config


class TestMySQLBasicOperations:
    """测试MySQL连接器的基本操作功能"""

    @pytest.mark.mysql
    @pytest.mark.minio
    @pytest.mark.asyncio
    async def test_basic_extract_and_load(
        self,
        mysql_engine: AsyncEngine,
        mysql_config: MySQLConfig,
        test_s3_config: S3Config,
    ) -> None:
        """测试基本的数据提取和加载功能"""
        table_name = "test_basic_extract_load"

        # 使用faker生成测试数据
        test_data = []
        for _ in range(10):
            test_data.append(
                {
                    "name": fake.name(),
                    "email": fake.email(),
                    "age": fake.random_int(min=18, max=80),
                    "city": fake.city(),
                    "phone": fake.phone_number(),
                }
            )

        # 创建测试表和数据
        async with mysql_engine.connect() as conn:
            await conn.execute(
                text(f"""
                CREATE TABLE IF NOT EXISTS {table_name} (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    name VARCHAR(100) NOT NULL,
                    email VARCHAR(255) UNIQUE,
                    age INT,
                    city VARCHAR(100),
                    phone VARCHAR(50),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            )

            # 插入faker生成的测试数据
            for data in test_data:
                await conn.execute(
                    text(f"""
                    INSERT INTO {table_name} (name, email, age, city, phone) VALUES
                    (:name, :email, :age, :city, :phone)
                    """),
                    data,
                )
            await conn.commit()

        try:
            # 测试数据提取
            extractor = MySQLExtractor(mysql_config)
            query_config = QueryConfig(table_name=table_name)
            export_config = ExportConfig(
                source_db=mysql_config,
                query=query_config,
                s3_target=test_s3_config,
                cursor_fetch_size=3,  # 小批次用于测试
                parquet_chunk_size=5,  # 小块用于测试
            )

            extracted_data = []
            async for chunk in extractor.extract_stream(query_config, export_config):
                assert isinstance(chunk, pd.DataFrame)
                assert not chunk.empty
                extracted_data.append(chunk)

            # 合并所有提取的数据
            combined_data = pd.concat(extracted_data, ignore_index=True)
            assert len(combined_data) == 10
            assert "id" in combined_data.columns
            assert "name" in combined_data.columns
            assert "email" in combined_data.columns
            assert "age" in combined_data.columns

            # 测试数据加载
            loader = MySQLLoader(mysql_config)
            target_table = f"{table_name}_copy"

            # 创建目标表
            async with mysql_engine.connect() as conn:
                await conn.execute(
                    text(f"""
                    CREATE TABLE IF NOT EXISTS {target_table} (
                        id INT PRIMARY KEY,
                        name VARCHAR(100) NOT NULL,
                        email VARCHAR(255) UNIQUE,
                        age INT,
                        city VARCHAR(100),
                        phone VARCHAR(50),
                        created_at TIMESTAMP
                    )
                """)
                )
                await conn.commit()

            # 移除自增ID列进行加载测试
            load_data = combined_data.copy()
            import_config = ImportConfig(
                table_name=target_table,
                s3_source=test_s3_config,
                target_db=mysql_config,
                conflict_strategy=ConflictStrategy.IGNORE,
            )

            await loader.load_chunk(load_data, import_config)

            # 验证加载的数据
            async with mysql_engine.connect() as conn:
                result = await conn.execute(
                    text(f"SELECT COUNT(*) FROM {target_table}")
                )
                count = result.scalar()
                assert count == 10

        finally:
            # 清理测试数据
            async with mysql_engine.connect() as conn:
                await conn.execute(text(f"DROP TABLE IF EXISTS {table_name}"))
                await conn.execute(text(f"DROP TABLE IF EXISTS {target_table}"))
                await conn.commit()

    @pytest.mark.mysql
    @pytest.mark.minio
    @pytest.mark.asyncio
    async def test_metadata_provider(
        self, mysql_engine: AsyncEngine, mysql_config: MySQLConfig
    ) -> None:
        """测试MySQL元数据提供者功能"""
        table_name = "test_metadata_table"

        # 创建测试表
        async with mysql_engine.connect() as conn:
            await conn.execute(
                text(f"""
                CREATE TABLE IF NOT EXISTS {table_name} (
                    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
                    name VARCHAR(100) NOT NULL COMMENT '用户名称',
                    email VARCHAR(255) UNIQUE COMMENT '电子邮箱',
                    age TINYINT UNSIGNED COMMENT '年龄',
                    score DECIMAL(5,2) DEFAULT 0.0 COMMENT '分数',
                    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
                    profile JSON COMMENT '用户档案',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    INDEX idx_name (name),
                    INDEX idx_email_age (email, age)
                ) COMMENT='用户信息表'
            """)
            )
            await conn.commit()

        try:
            metadata_provider = MySQLMetadataProvider(mysql_config)
            metadata = await metadata_provider.get_table_metadata(table_name)

            assert metadata is not None
            assert metadata.table_name == table_name
            assert len(metadata.columns) > 0

            # 验证关键列
            column_names = [col.name for col in metadata.columns]
            assert "id" in column_names
            assert "name" in column_names
            assert "email" in column_names

            # 验证主键
            primary_keys = [col.name for col in metadata.columns if col.is_primary_key]
            assert "id" in primary_keys

        finally:
            # 清理测试数据
            async with mysql_engine.connect() as conn:
                await conn.execute(text(f"DROP TABLE IF EXISTS {table_name}"))
                await conn.commit()


class TestMySQLDataTypes:
    """测试MySQL各种数据类型的兼容性"""

    @pytest.mark.mysql
    @pytest.mark.minio
    @pytest.mark.asyncio
    async def test_numeric_types_extract_load(
        self,
        mysql_engine: AsyncEngine,
        mysql_config: MySQLConfig,
        test_s3_config: S3Config,
    ) -> None:
        """测试数值类型的数据提取和加载"""
        table_name = "test_numeric_types"

        # 使用faker生成数值测试数据
        test_data = []
        for _ in range(20):
            test_data.append(
                {
                    "tinyint_val": fake.random_int(min=-128, max=127),
                    "smallint_val": fake.random_int(min=-32768, max=32767),
                    "int_val": fake.random_int(min=-2147483648, max=2147483647),
                    "bigint_val": fake.random_int(
                        min=-9223372036854775808, max=9223372036854775807
                    ),
                    "decimal_val": fake.pydecimal(
                        left_digits=5, right_digits=2, positive=True
                    ),
                    "float_val": fake.pyfloat(min_value=0.0, max_value=1000.0),
                    "double_val": fake.pyfloat(min_value=0.0, max_value=1000000.0),
                }
            )

        # 创建测试表
        async with mysql_engine.connect() as conn:
            await conn.execute(
                text(f"""
                CREATE TABLE IF NOT EXISTS {table_name} (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    tinyint_val TINYINT,
                    smallint_val SMALLINT,
                    int_val INT,
                    bigint_val BIGINT,
                    decimal_val DECIMAL(7,2),
                    float_val FLOAT,
                    double_val DOUBLE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            )

            # 插入测试数据
            for data in test_data:
                await conn.execute(
                    text(f"""
                    INSERT INTO {table_name}
                    (tinyint_val, smallint_val, int_val, bigint_val, decimal_val, float_val, double_val)
                    VALUES (:tinyint_val, :smallint_val, :int_val, :bigint_val, :decimal_val, :float_val, :double_val)
                    """),
                    data,
                )
            await conn.commit()

        try:
            # 测试数据提取
            extractor = MySQLExtractor(mysql_config)
            query_config = QueryConfig(table_name=table_name)
            export_config = ExportConfig(
                source_db=mysql_config,
                query=query_config,
                s3_target=test_s3_config,
            )

            extracted_data = []
            async for chunk in extractor.extract_stream(query_config, export_config):
                extracted_data.append(chunk)

            combined_data = pd.concat(extracted_data, ignore_index=True)
            assert len(combined_data) == 20

            # 验证数值类型
            assert "tinyint_val" in combined_data.columns
            assert "decimal_val" in combined_data.columns
            assert "float_val" in combined_data.columns

        finally:
            # 清理测试数据
            async with mysql_engine.connect() as conn:
                await conn.execute(text(f"DROP TABLE IF EXISTS {table_name}"))
                await conn.commit()

    @pytest.mark.mysql
    @pytest.mark.minio
    @pytest.mark.asyncio
    async def test_string_types_extract_load(
        self,
        mysql_engine: AsyncEngine,
        mysql_config: MySQLConfig,
        test_s3_config: S3Config,
    ) -> None:
        """测试字符串类型的数据提取和加载"""
        table_name = "test_string_types"

        # 使用faker生成字符串测试数据
        test_data = []
        for _ in range(15):
            test_data.append(
                {
                    "char_val": fake.random_letter() * 10,  # 固定长度
                    "varchar_val": fake.text(max_nb_chars=100),
                    "text_val": fake.text(max_nb_chars=500),
                    "longtext_val": fake.text(max_nb_chars=1000),
                    "binary_val": fake.binary(length=20),
                    "varbinary_val": fake.binary(length=50),
                    "blob_val": fake.binary(length=100),
                }
            )

        # 创建测试表
        async with mysql_engine.connect() as conn:
            await conn.execute(
                text(f"""
                CREATE TABLE IF NOT EXISTS {table_name} (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    char_val CHAR(10),
                    varchar_val VARCHAR(100),
                    text_val TEXT,
                    longtext_val LONGTEXT,
                    binary_val BINARY(20),
                    varbinary_val VARBINARY(50),
                    blob_val BLOB,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            )

            # 插入测试数据
            for data in test_data:
                await conn.execute(
                    text(f"""
                    INSERT INTO {table_name}
                    (char_val, varchar_val, text_val, longtext_val, binary_val, varbinary_val, blob_val)
                    VALUES (:char_val, :varchar_val, :text_val, :longtext_val, :binary_val, :varbinary_val, :blob_val)
                    """),
                    data,
                )
            await conn.commit()

        try:
            # 测试数据提取
            extractor = MySQLExtractor(mysql_config)
            query_config = QueryConfig(table_name=table_name)
            export_config = ExportConfig(
                source_db=mysql_config,
                query=query_config,
                s3_target=test_s3_config,
            )

            extracted_data = []
            async for chunk in extractor.extract_stream(query_config, export_config):
                extracted_data.append(chunk)

            combined_data = pd.concat(extracted_data, ignore_index=True)
            assert len(combined_data) == 15

            # 验证字符串类型
            assert "char_val" in combined_data.columns
            assert "varchar_val" in combined_data.columns
            assert "text_val" in combined_data.columns

        finally:
            # 清理测试数据
            async with mysql_engine.connect() as conn:
                await conn.execute(text(f"DROP TABLE IF EXISTS {table_name}"))
                await conn.commit()


class TestMySQLBatchExport:
    """测试MySQL分批导出功能"""

    @pytest.mark.mysql
    @pytest.mark.minio
    @pytest.mark.asyncio
    async def test_small_batch_processing(
        self,
        mysql_engine: AsyncEngine,
        mysql_config: MySQLConfig,
        test_s3_config: S3Config,
    ) -> None:
        """测试小批次数据处理"""
        table_name = "test_small_batch"

        # 使用faker生成小批次测试数据
        test_data = []
        for i in range(25):
            test_data.append(
                {
                    "data_value": f"数据{i + 1}",
                    "batch_number": (i // 5) + 1,
                    "description": fake.sentence(),
                    "priority": fake.random_int(min=1, max=10),
                }
            )

        # 创建测试表
        async with mysql_engine.connect() as conn:
            await conn.execute(
                text(f"""
                CREATE TABLE IF NOT EXISTS {table_name} (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    data_value VARCHAR(100),
                    batch_number INT,
                    description TEXT,
                    priority INT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            )

            # 插入测试数据
            for data in test_data:
                await conn.execute(
                    text(f"""
                    INSERT INTO {table_name}
                    (data_value, batch_number, description, priority)
                    VALUES (:data_value, :batch_number, :description, :priority)
                    """),
                    data,
                )
            await conn.commit()

        try:
            # 测试小批次数据提取
            extractor = MySQLExtractor(mysql_config)
            query_config = QueryConfig(table_name=table_name)
            export_config = ExportConfig(
                source_db=mysql_config,
                query=query_config,
                s3_target=test_s3_config,
                cursor_fetch_size=5,  # 每批5条记录
                parquet_chunk_size=10,  # 每块10条记录
            )

            extracted_data = []
            chunk_count = 0
            async for chunk in extractor.extract_stream(query_config, export_config):
                assert isinstance(chunk, pd.DataFrame)
                assert not chunk.empty
                assert len(chunk) <= 10  # 每块最多10条记录
                extracted_data.append(chunk)
                chunk_count += 1

            # 验证批次处理
            assert chunk_count > 1  # 应该有多个批次
            combined_data = pd.concat(extracted_data, ignore_index=True)
            assert len(combined_data) == 25

        finally:
            # 清理测试数据
            async with mysql_engine.connect() as conn:
                await conn.execute(text(f"DROP TABLE IF EXISTS {table_name}"))
                await conn.commit()


class TestMySQLConflictHandling:
    """测试MySQL主键冲突处理"""

    @pytest.mark.mysql
    @pytest.mark.minio
    @pytest.mark.asyncio
    async def test_ignore_strategy(
        self,
        mysql_engine: AsyncEngine,
        mysql_config: MySQLConfig,
        test_s3_config: S3Config,
    ) -> None:
        """测试IGNORE冲突策略"""
        table_name = "test_ignore_strategy"

        # 使用faker生成测试数据
        test_data = []
        for i in range(10):
            test_data.append(
                {
                    "id": i + 1,  # 明确指定ID
                    "name": fake.name(),
                    "email": fake.email(),
                    "age": fake.random_int(min=18, max=80),
                    "city": fake.city(),
                }
            )

        # 创建测试表
        async with mysql_engine.connect() as conn:
            await conn.execute(
                text(f"""
                CREATE TABLE IF NOT EXISTS {table_name} (
                    id INT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    email VARCHAR(255) UNIQUE,
                    age INT,
                    city VARCHAR(100),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            )

            # 插入初始数据
            for data in test_data[:5]:  # 只插入前5条
                await conn.execute(
                    text(f"""
                    INSERT INTO {table_name}
                    (id, name, email, age, city)
                    VALUES (:id, :name, :email, :age, :city)
                    """),
                    data,
                )
            await conn.commit()

        try:
            # 测试IGNORE策略加载
            loader = MySQLLoader(mysql_config)

            # 准备包含重复ID的数据
            duplicate_data = pd.DataFrame(test_data)  # 包含所有10条记录

            import_config = ImportConfig(
                table_name=table_name,
                s3_source=test_s3_config,
                target_db=mysql_config,
                conflict_strategy=ConflictStrategy.IGNORE,
            )

            # 加载数据（应该忽略重复的ID）
            await loader.load_chunk(duplicate_data, import_config)

            # 验证结果
            async with mysql_engine.connect() as conn:
                result = await conn.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                count = result.scalar()
                assert count == 10  # 应该有10条记录，重复的主键被忽略但新记录被插入

        finally:
            # 清理测试数据
            async with mysql_engine.connect() as conn:
                await conn.execute(text(f"DROP TABLE IF EXISTS {table_name}"))
                await conn.commit()
