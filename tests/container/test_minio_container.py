#!/usr/bin/env python3

"""
MinIO 容器测试 - 验证容器启动成功
"""

import pytest


@pytest.mark.minio
@pytest.mark.asyncio
async def test_minio_file_upload_download(minio_client):
    """测试 MinIO 容器启动成功 - 文件上传下载"""
    bucket_name = "test-bucket"
    test_key = "test-file.txt"
    test_content = b"Hello, <PERSON>IO!"

    # 创建桶
    await minio_client.create_bucket(Bucket=bucket_name)

    # 上传文件
    await minio_client.put_object(Bucket=bucket_name, Key=test_key, Body=test_content)

    # 下载文件
    response = await minio_client.get_object(Bucket=bucket_name, Key=test_key)
    content = await response["Body"].read()
    assert content == test_content

    # 清理
    await minio_client.delete_object(Bucket=bucket_name, Key=test_key)
    await minio_client.delete_bucket(Bucket=bucket_name)
