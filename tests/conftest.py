#!/usr/bin/env python3

import os
from collections.abc import AsyncGenerator
from typing import Any

import aioboto3  # type: ignore
import pytest
import pytest_asyncio
from clickhouse_connect import get_async_client
from clickhouse_connect.driver.asyncclient import AsyncClient
from pydantic import SecretStr
from sqlalchemy.ext.asyncio import AsyncEngine, create_async_engine
from testcontainers.clickhouse import <PERSON>lickHouseContainer  # type: ignore
from testcontainers.minio import MinioContainer  # type: ignore
from testcontainers.mongodb import MongoDbContainer  # type: ignore
from testcontainers.mysql import MySqlContainer  # type: ignore
from testcontainers.postgres import PostgresContainer  # type: ignore

from datax.models import ClickHouseConfig, MySQLConfig, PostgresConfig, S3Config

# 条件导入motor，避免Python 3.13兼容性问题
try:
    from motor.motor_asyncio import AsyncIOMotorClient

    MOTOR_AVAILABLE = True
except (ImportError, AttributeError) as e:
    # 在Python 3.13中，motor可能因为asyncio.coroutine被移除而失败
    print(f"Warning: motor not available ({e}). MongoDB tests will be skipped.")
    MOTOR_AVAILABLE = False
    AsyncIOMotorClient = Any  # type: ignore


def setup_docker_host() -> None:
    """
    动态检测docker.sock位置并设置DOCKER_HOST环境变量。
    支持多个操作系统平台，包括macOS、Linux和Windows。

    该函数会检查常见的docker.sock路径，如果找到则设置DOCKER_HOST环境变量。
    如果没有找到，则使用默认的Docker Desktop设置。

    支持的路径包括：
    - macOS (Docker Desktop): ~/.docker/run/docker.sock
    - Linux (标准位置): /var/run/docker.sock
    - Linux (用户级Docker): ~/.docker/run/docker.sock
    - Windows WSL2: /var/run/docker.sock

    返回值:
        None: 无返回值，仅设置环境变量
    """
    # 常见的docker.sock位置
    docker_sock_paths = [
        # macOS (Docker Desktop)
        os.path.expanduser("~/.docker/run/docker.sock"),
        # Linux (标准位置)
        "/var/run/docker.sock",
        # Linux (用户级Docker)
        os.path.expanduser("~/.docker/run/docker.sock"),
        # Windows WSL2 (通过WSL访问)
        "/var/run/docker.sock",
    ]

    # 检查哪个路径存在
    for path in docker_sock_paths:
        if os.path.exists(path):
            docker_sock = f"unix://{path}"
            os.environ["DOCKER_HOST"] = docker_sock
            print(f"Docker socket found at: {path}")
            return

    # 如果没有找到docker.sock，尝试使用默认的Docker Desktop设置
    # 这通常适用于macOS和Windows
    default_docker_host = "tcp://localhost:2375"
    os.environ["DOCKER_HOST"] = default_docker_host
    print(f"Docker socket not found, using default: {default_docker_host}")


# 在模块导入时执行一次
setup_docker_host()


@pytest.fixture(scope="session")
def anyio_backend() -> str:
    """配置anyio后端为asyncio，避免事件循环冲突"""
    return "asyncio"


def _has_marker_in_session(session: pytest.Session, marker_name: str) -> bool:
    """
    检查当前测试会话中是否有任何测试用例使用了指定的标记。
    用于 session scope 的 fixture 来决定是否需要启动对应的容器。

    参数:
        session (pytest.Session): pytest测试会话对象，包含所有测试用例信息
        marker_name (str): 要检查的标记名称，例如 'postgres', 'mysql' 等

    返回值:
        bool: 如果会话中有任何测试用例使用了指定标记则返回True，否则返回False
    """
    for item in session.items:
        if item.get_closest_marker(marker_name):
            return True
    return False


@pytest_asyncio.fixture(scope="function")
async def postgres_engine(
    request: pytest.FixtureRequest,
) -> AsyncGenerator[AsyncEngine]:
    """
    基于 PostgreSQL 容器，创建一个异步 SQLAlchemy 引擎。
    使用 asyncpg 驱动进行异步数据库操作。

    该fixture会启动一个PostgreSQL容器，创建异步SQLAlchemy引擎，
    并在测试函数结束时自动清理资源。

    参数:
        request (pytest.FixtureRequest): pytest fixture请求对象，用于检查测试会话中的标记

    返回值:
        AsyncGenerator[AsyncEngine]: 异步生成器，yield一个配置好的异步SQLAlchemy引擎

    使用条件:
        只有当测试会话中有测试用例使用了 @pytest.mark.postgres 标记时才会启动容器
    """
    # 检查当前测试会话中是否有使用 postgres 标记的测试
    if not _has_marker_in_session(request.session, "postgres"):
        pytest.skip("No tests marked with @pytest.mark.postgres in this session")

    with PostgresContainer("postgres:17-alpine") as container:
        # 将同步 URL 转换为异步 URL (使用 asyncpg)
        sync_url = container.get_connection_url()
        print(f"Original URL: {sync_url}")
        # testcontainers 返回的 URL 可能包含 psycopg2 驱动，需要替换
        if "postgresql+psycopg2://" in sync_url:
            async_url = sync_url.replace(
                "postgresql+psycopg2://", "postgresql+asyncpg://"
            )
        else:
            async_url = sync_url.replace("postgresql://", "postgresql+asyncpg://")
        print(f"Async URL: {async_url}")

        engine = create_async_engine(async_url)
        yield engine
        await engine.dispose()


@pytest_asyncio.fixture(scope="function")
async def mysql_engine(request: pytest.FixtureRequest) -> AsyncGenerator[AsyncEngine]:
    """
    基于 MySQL 容器，创建一个异步 SQLAlchemy 引擎。
    使用 asyncmy 驱动进行异步数据库操作。

    该fixture会启动一个MySQL容器，创建异步SQLAlchemy引擎，
    并在测试函数结束时自动清理资源。

    参数:
        request (pytest.FixtureRequest): pytest fixture请求对象，用于检查测试会话中的标记

    返回值:
        AsyncGenerator[AsyncEngine]: 异步生成器，yield一个配置好的异步SQLAlchemy引擎

    使用条件:
        只有当测试会话中有测试用例使用了 @pytest.mark.mysql 标记时才会启动容器
    """
    # 检查当前测试会话中是否有使用 mysql 标记的测试
    if not _has_marker_in_session(request.session, "mysql"):
        pytest.skip("No tests marked with @pytest.mark.mysql in this session")

    with MySqlContainer("mysql:8") as container:
        # 将同步 URL 转换为异步 URL (使用 asyncmy)
        sync_url = container.get_connection_url()
        # testcontainers 可能返回不同格式的 URL，需要处理多种情况
        if "mysql+pymysql://" in sync_url:
            async_url = sync_url.replace("mysql+pymysql://", "mysql+asyncmy://")
        elif "mysql://" in sync_url:
            async_url = sync_url.replace("mysql://", "mysql+asyncmy://")
        else:
            # 如果是其他格式，强制使用 asyncmy
            async_url = sync_url.replace("mysql+", "mysql+asyncmy+", 1)
        engine = create_async_engine(async_url)
        yield engine
        await engine.dispose()


@pytest.fixture(scope="function")
async def mysql_container_info(
    request: pytest.FixtureRequest,
) -> AsyncGenerator[dict[str, str | int]]:
    """
    基于 MySQL 容器，提供连接信息。
    这是一个同步 fixture，避免异步事件循环问题。

    该fixture会启动一个MySQL容器，并提供连接信息。
    容器会在测试函数结束时自动停止。

    参数:
        request (pytest.FixtureRequest): pytest fixture请求对象，用于检查测试会话中的标记

    返回值:
        AsyncGenerator[dict[str, str | int]]: 异步生成器，yield一个包含连接信息的字典
        字典包含以下键值对：
        - url (str): MySQL连接URL
        - host (str): 主机地址
        - port (int): 端口号
        - username (str): 用户名
        - password (str): 密码
        - database (str): 数据库名

    使用条件:
        只有当测试会话中有测试用例使用了 @pytest.mark.mysql 标记时才会启动容器
    """
    # 检查当前测试会话中是否有使用 mysql 标记的测试
    if not _has_marker_in_session(request.session, "mysql"):
        pytest.skip("No tests marked with @pytest.mark.mysql in this session")

    with MySqlContainer("mysql:8") as container:
        # 获取容器连接信息
        sync_url = container.get_connection_url()
        host = container.get_container_host_ip()
        port = container.get_exposed_port(3306)

        # MySQL 8容器的默认凭据
        username = container.username
        password = container.password
        database = container.dbname

        yield {
            "url": sync_url,
            "host": host,
            "port": port,
            "username": username,
            "password": password,
            "database": database,
        }


@pytest.fixture(scope="function")
def mysql_config(mysql_engine: AsyncEngine) -> MySQLConfig:
    """
    基于 MySQL 引擎，创建 MySQL 配置对象。

    从现有的mysql_engine中提取连接信息，确保使用同一个MySQL容器。
    使用testcontainers MySQL的默认凭据。

    参数:
        mysql_engine: MySQL SQLAlchemy 引擎

    返回值:
        MySQLConfig: 配置好的MySQL配置对象
    """
    from urllib.parse import urlparse

    # 从引擎URL中提取连接信息
    url = str(mysql_engine.url)
    parsed = urlparse(url.replace("mysql+asyncmy://", "mysql://"))

    # testcontainers MySQL的默认凭据
    return MySQLConfig(
        host=parsed.hostname or "localhost",
        port=parsed.port or 3306,
        username="test",  # testcontainers MySQL默认用户名
        password=SecretStr("test"),  # testcontainers MySQL默认密码
        database="test",  # testcontainers MySQL默认数据库
    )


@pytest.fixture(scope="function")
async def clickhouse_container_info(
    request: pytest.FixtureRequest,
) -> AsyncGenerator[dict[str, str | int]]:
    """
    基于 ClickHouse 容器，提供连接信息。
    这是一个同步 fixture，避免异步事件循环问题。

    该fixture会启动一个ClickHouse容器，并提供连接URL和HTTP端口信息。
    容器会在测试函数结束时自动停止。

    参数:
        request (pytest.FixtureRequest): pytest fixture请求对象，用于检查测试会话中的标记

    返回值:
        AsyncGenerator[dict[str, str | int]]: 异步生成器，yield一个包含连接信息的字典
        字典包含以下键值对：
        - url (str): ClickHouse连接URL，格式类似 clickhouse://test:test@localhost:55020/test
        - http_port (int): ClickHouse HTTP端口号

    使用条件:
        只有当测试会话中有测试用例使用了 @pytest.mark.clickhouse 标记时才会启动容器
    """
    # 检查当前测试会话中是否有使用 clickhouse 标记的测试
    if not _has_marker_in_session(request.session, "clickhouse"):
        pytest.skip("No tests marked with @pytest.mark.clickhouse in this session")

    with ClickHouseContainer("clickhouse/clickhouse-server:25.1-alpine") as container:
        url = container.get_connection_url()
        http_port = container.get_exposed_port(8123)
        yield {"url": url, "http_port": http_port}


@pytest.fixture(scope="function")
def clickhouse_config(clickhouse_container_info: dict) -> ClickHouseConfig:
    """
    基于 ClickHouse 容器信息，创建 ClickHouse 配置对象。

    该fixture解析容器连接信息，创建一个ClickHouseConfig配置对象，
    用于在测试中配置ClickHouse连接参数。

    参数:
        clickhouse_container_info (dict): ClickHouse容器信息字典，包含url和http_port

    返回值:
        ClickHouseConfig: 配置好的ClickHouse配置对象，包含以下字段：
        - host (str): 主机地址
        - port (int): HTTP端口号
        - username (str): 用户名
        - password (SecretStr | None): 密码，如果为空则为None
        - database (str): 数据库名称
    """
    # 解析容器 URL 来获取连接参数
    # clickhouse_container_url 格式类似: clickhouse://test:test@localhost:55020/test
    from urllib.parse import urlparse

    parsed = urlparse(clickhouse_container_info["url"])

    password = parsed.password or ""
    return ClickHouseConfig(
        host=parsed.hostname or "localhost",
        port=clickhouse_container_info["http_port"],  # 使用HTTP端口
        username=parsed.username or "default",
        password=SecretStr(password) if password else None,
        database=parsed.path.lstrip("/") or "default",
    )


@pytest_asyncio.fixture(scope="function")
async def clickhouse_client(
    clickhouse_container_info: dict,
) -> AsyncGenerator[AsyncClient]:
    """
    基于 ClickHouse 容器信息，创建一个异步客户端。
    使用 clickhouse_connect 驱动进行异步数据库操作。

    该fixture会创建一个ClickHouse异步客户端，连接到测试容器，
    并在测试函数结束时自动关闭连接。

    参数:
        clickhouse_container_info (dict): ClickHouse容器信息字典，包含url和http_port

    返回值:
        AsyncGenerator[AsyncClient]: 异步生成器，yield一个配置好的ClickHouse异步客户端
    """
    from urllib.parse import urlparse

    parsed = urlparse(clickhouse_container_info["url"])
    password = parsed.password or ""

    client = await get_async_client(
        host=parsed.hostname or "localhost",
        port=clickhouse_container_info["http_port"],
        username=parsed.username or "default",
        password=password,
        database=parsed.path.lstrip("/") or "default",
    )

    yield client
    await client.close()


@pytest.fixture(scope="function")
async def mongodb_container_url(request: pytest.FixtureRequest) -> AsyncGenerator[str]:
    """
    基于 MongoDB 容器，提供连接 URL。
    这是一个同步 fixture，避免异步事件循环问题。

    该fixture会启动一个MongoDB容器，并提供连接URL。
    容器会在测试函数结束时自动停止。

    参数:
        request (pytest.FixtureRequest): pytest fixture请求对象，用于检查测试会话中的标记

    返回值:
        AsyncGenerator[str]: 异步生成器，yield一个MongoDB连接URL字符串

    使用条件:
        只有当测试会话中有测试用例使用了 @pytest.mark.mongodb 标记时才会启动容器
    """
    # 检查当前测试会话中是否有使用 mongodb 标记的测试
    if not _has_marker_in_session(request.session, "mongodb"):
        pytest.skip("No tests marked with @pytest.mark.mongodb in this session")

    with MongoDbContainer("mongo:8") as container:
        connection_url = container.get_connection_url()
        yield connection_url


@pytest_asyncio.fixture(scope="function")
async def mongodb_client(
    mongodb_container_url: str,
) -> AsyncGenerator[Any]:
    """
    基于 MongoDB 容器 URL，创建一个异步客户端。
    使用 motor 驱动进行异步数据库操作。

    该fixture会创建一个MongoDB异步客户端，连接到测试容器，
    并在测试函数结束时自动关闭连接。

    参数:
        mongodb_container_url (str): MongoDB容器连接URL

    返回值:
        AsyncGenerator[AsyncIOMotorClient]: 异步生成器，yield一个配置好的MongoDB异步客户端
    """
    if not MOTOR_AVAILABLE:
        pytest.skip("motor is not available, skipping MongoDB tests")

    client = AsyncIOMotorClient(mongodb_container_url)  # type: ignore

    yield client
    client.close()


@pytest.fixture(scope="function")
async def minio_container_config(
    request: pytest.FixtureRequest,
) -> AsyncGenerator[dict[str, str]]:
    """
    基于 MinIO 容器，提供连接配置。
    这是一个同步 fixture，避免异步事件循环问题。

    该fixture会启动一个MinIO容器，并提供连接配置信息。
    容器会在测试函数结束时自动停止。

    参数:
        request (pytest.FixtureRequest): pytest fixture请求对象，用于检查测试会话中的标记

    返回值:
        AsyncGenerator[dict[str, str]]: 异步生成器，yield一个包含MinIO连接配置的字典
        字典包含以下键值对：
        - endpoint_url (str): MinIO服务端点URL
        - aws_access_key_id (str): 访问密钥ID
        - aws_secret_access_key (str): 访问密钥

    使用条件:
        只有当测试会话中有测试用例使用了 @pytest.mark.minio 标记时才会启动容器
    """
    # 检查当前测试会话中是否有使用 minio 标记的测试
    if not _has_marker_in_session(request.session, "minio"):
        pytest.skip("No tests marked with @pytest.mark.minio in this session")

    with MinioContainer("minio/minio:latest") as container:
        # 获取 MinIO 配置
        config = container.get_config()
        host = container.get_container_host_ip()
        port = container.get_exposed_port(9000)
        endpoint_url = f"http://{host}:{port}"

        yield {
            "endpoint_url": endpoint_url,
            "aws_access_key_id": config["access_key"],
            "aws_secret_access_key": config["secret_key"],
        }


@pytest_asyncio.fixture(scope="function")
async def minio_client(
    minio_container_config: dict,
) -> AsyncGenerator[Any]:
    """
    基于 MinIO 容器配置，创建一个异步 S3 客户端。
    使用 aioboto3 进行异步 S3 操作。

    该fixture会创建一个异步S3客户端，连接到MinIO测试容器，
    并在测试函数结束时自动关闭连接。

    参数:
        minio_container_config (dict): MinIO容器配置字典，包含endpoint_url、access_key和secret_key

    返回值:
        AsyncGenerator[aioboto3.Session.client]: 异步生成器，yield一个配置好的异步S3客户端
    """
    # 创建异步 S3 客户端
    session = aioboto3.Session()
    client = await session.client(
        "s3",
        endpoint_url=minio_container_config["endpoint_url"],
        aws_access_key_id=minio_container_config["aws_access_key_id"],
        aws_secret_access_key=minio_container_config["aws_secret_access_key"],
        use_ssl=False,
    ).__aenter__()

    yield client
    await client.__aexit__(None, None, None)


@pytest.fixture(scope="function")
def s3_config(minio_container_config: dict) -> S3Config:
    """
    基于 MinIO 容器配置，创建 S3 配置对象。

    该fixture会创建一个S3Config配置对象，用于在测试中配置S3连接参数。

    参数:
        minio_container_config (dict): MinIO容器配置字典，包含endpoint_url、access_key和secret_key

    返回值:
        S3Config: 配置好的S3配置对象，包含以下字段：
        - endpoint_url (str): S3服务端点URL
        - access_key (str): 访问密钥ID
        - secret_key (SecretStr): 访问密钥
        - bucket (str): 默认存储桶名称，设置为"test-bucket"
        - key (str): 默认对象键前缀，设置为"test/"
        - region (str): AWS区域，设置为"us-east-1"
    """
    return S3Config(
        endpoint_url=minio_container_config["endpoint_url"],
        access_key=minio_container_config["aws_access_key_id"],
        secret_key=SecretStr(minio_container_config["aws_secret_access_key"]),
        bucket="test-bucket",  # 使用默认的测试存储桶名称
        key="test/",
        region="us-east-1",
    )


@pytest_asyncio.fixture(scope="function")
async def postgres_config(postgres_engine) -> PostgresConfig:
    """
    基于 PostgreSQL 容器引擎，创建 PostgreSQL 配置对象。

    该fixture会从PostgreSQL容器引擎中提取连接信息，
    创建一个PostgresConfig配置对象，用于在测试中配置PostgreSQL连接参数。

    参数:
        postgres_engine: PostgreSQL异步引擎对象

    返回值:
        PostgresConfig: 配置好的PostgreSQL配置对象
    """
    # 从引擎URL中提取连接信息
    # 注意：不能直接从str(url)获取密码，因为SQLAlchemy会隐藏密码
    url = postgres_engine.url

    # 直接从URL对象获取连接信息
    user = url.username
    password = url.password  # 这里可以获取真实密码
    host = url.host
    port = url.port
    database = url.database

    return PostgresConfig(
        username=user,
        password=SecretStr(password),
        host=host,
        port=int(port),
        database=database,
        schema_name="public",
    )
