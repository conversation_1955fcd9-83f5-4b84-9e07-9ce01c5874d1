{"python.venvPath": "${workspaceFolder}/.venv", "python.defaultInterpreterPath": "${workspaceFolder}/.venv/bin/python", "python.terminal.activateEnvironment": true, "python.analysis.extraPaths": ["${workspaceFolder}/src"], "python.formatting.provider": "ruff", "editor.formatOnSave": true, "python.analysis.autoImportCompletions": true, "python.analysis.typeCheckingMode": "basic", "python.analysis.autoSearchPaths": true, "python.analysis.diagnosticMode": "openFilesOnly", "python.analysis.stubPath": "${workspaceFolder}/typings", "python.analysis.include": ["src/**/*.py"], "python.analysis.exclude": ["**/node_modules", "**/__pycache__", "**/.git", "**/.venv", "**/tests", "**/typestubs", "**/stubs", "**/experimental", "**/oldstuff", "**/typings"], "[python]": {"editor.formatOnSave": true, "editor.defaultFormatter": "charliermarsh.ruff"}, "cursorpyright.analysis.autoImportCompletions": true, "cursorpyright.analysis.autoSearchPaths": true, "cursorpyright.analysis.diagnosticMode": "openFilesOnly", "cursorpyright.analysis.exclude": ["**/node_modules", "**/__pycache__", "**/.git", "**/.venv", "**/tests", "**/typestubs", "**/stubs", "**/experimental", "**/oldstuff", "**/typings"], "cursorpyright.analysis.extraPaths": ["${workspaceFolder}/src"], "cursorpyright.analysis.include": ["src/**/*.py"], "cursorpyright.analysis.stubPath": "${workspaceFolder}/typings", "cursorpyright.analysis.typeCheckingMode": "basic"}