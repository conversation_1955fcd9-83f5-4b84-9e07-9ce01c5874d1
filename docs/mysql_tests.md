# MySQL 集成测试说明

本文档介绍如何运行MySQL连接器的完整集成测试套件。

## 测试覆盖范围

### 1. 基础功能测试 (`TestMySQLBasicOperations`)
- **连接测试**: 验证MySQL连接建立和关闭
- **基础导入导出**: 测试完整的数据提取和加载流程
- **元数据查询**: 测试表结构、索引、约束等元数据获取功能

### 2. 数据类型兼容性测试 (`TestMySQLDataTypes`)
- **数值类型**: INT, BIGINT, DECIMAL, FLOAT, DOUBLE等
- **字符串类型**: VARCHAR, TEXT, ENUM, SET等，包含中文字符测试
- **特殊类型**: JSON, BOOLEAN, BLOB等

### 3. 分批导出测试 (`TestMySQLBatchExport`)
- **小批次处理**: 测试小批次数据的流式处理
- **大数据集流式处理**: 验证大量数据的内存优化处理
- **条件查询导出**: 测试带WHERE条件和列选择的查询

### 4. 冲突处理测试 (`TestMySQLConflictHandling`)
- **IGNORE策略**: 测试INSERT IGNORE行为
- **REPLACE策略**: 测试REPLACE INTO行为
- **唯一约束冲突**: 测试多种唯一约束的冲突处理

## 运行测试

### 前置条件
1. 确保Docker已安装并运行
2. 安装项目依赖：`uv sync`
3. 确保测试环境变量已设置

### 运行单个测试类
```bash
# 运行基础功能测试
pytest tests/integration/test_mysql_import_export.py::TestMySQLBasicOperations -v -s

# 运行数据类型测试
pytest tests/integration/test_mysql_import_export.py::TestMySQLDataTypes -v -s

# 运行分批导出测试
pytest tests/integration/test_mysql_import_export.py::TestMySQLBatchExport -v -s

# 运行冲突处理测试
pytest tests/integration/test_mysql_import_export.py::TestMySQLConflictHandling -v -s
```

### 运行所有MySQL测试
```bash
# 运行所有MySQL集成测试
pytest tests/integration/test_mysql_import_export.py -v -s

# 只运行标记为mysql的测试
pytest -m mysql tests/integration/test_mysql_import_export.py -v -s
```

### 运行单个测试方法
```bash
# 测试基础连接
pytest tests/integration/test_mysql_import_export.py::TestMySQLBasicOperations::test_mysql_connection -v -s

# 测试数值类型
pytest tests/integration/test_mysql_import_export.py::TestMySQLDataTypes::test_numeric_types_extract_load -v -s

# 测试冲突处理
pytest tests/integration/test_mysql_import_export.py::TestMySQLConflictHandling::test_ignore_strategy -v -s
```

### 使用便捷脚本
```bash
# 运行所有测试
python scripts/run_mysql_tests.py --all

# 运行基础功能测试
python scripts/run_mysql_tests.py --basic

# 运行数据类型测试
python scripts/run_mysql_tests.py --datatypes

# 运行分批导出测试
python scripts/run_mysql_tests.py --batch

# 运行冲突处理测试
python scripts/run_mysql_tests.py --conflict

# 运行特定测试方法
python scripts/run_mysql_tests.py --method connection
```

## 测试环境

### 容器服务
测试会自动启动以下Docker容器：
- **MySQL 8.0**: 用于数据库操作测试
- **MinIO**: 用于S3存储模拟

### 测试配置
- 数据库字符集：`utf8mb4`
- 批次大小：可配置的`cursor_fetch_size`和`parquet_chunk_size`
- 使用Faker生成中英文混合测试数据

## 测试策略

### 数据隔离
- 每个测试方法使用独立的表名
- 测试结束后自动清理创建的表
- 避免测试用例间的数据污染

### 错误处理
- 使用try-finally确保资源清理
- 测试异常情况和边界条件
- 验证错误消息和状态码

### 性能考虑
- 小批次配置用于快速测试
- 大数据集测试验证内存效率
- 流式处理验证避免内存溢出

## 调试指南

### 查看详细日志
```bash
pytest tests/integration/test_mysql_import_export.py -v -s --log-level=DEBUG
```

### 保留测试数据（调试时）
可以临时注释掉测试方法中的清理代码：
```python
# finally:
#     # 清理测试数据
#     async with mysql_engine.connect() as conn:
#         await conn.execute(text(f"DROP TABLE IF EXISTS {table_name}"))
#         await conn.commit()
```

### 检查容器状态
```bash
# 查看运行的容器
docker ps

# 查看MySQL容器日志
docker logs <mysql_container_id>

# 连接到MySQL容器
docker exec -it <mysql_container_id> mysql -u root -p
```

## 常见问题

### 1. 容器启动失败
- 检查Docker服务是否运行
- 确保端口没有被占用
- 查看Docker日志获取错误信息

### 2. 编码问题
- 确保MySQL容器使用utf8mb4字符集
- 检查中文字符的插入和查询

### 3. 内存不足
- 调整批次大小参数
- 检查大数据集测试的内存使用

### 4. 异步事件循环冲突
- 确保使用正确的pytest-asyncio配置
- 检查fixture作用域设置

## 扩展测试

如需添加新的测试场景：

1. **创建新的测试类**：按功能分组
2. **添加适当的标记**：使用`@pytest.mark.mysql`和`@pytest.mark.minio`
3. **实现数据清理**：确保测试后清理资源
4. **添加详细文档**：说明测试目的和预期结果

## 贡献指南

提交测试相关的代码时，请确保：
- 遵循现有的测试模式
- 添加适当的断言和验证
- 包含中文注释和文档
- 测试用例具有良好的隔离性 