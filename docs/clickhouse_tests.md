# ClickHouse 导入导出测试指南

## 概述

本文档描述了如何运行ClickHouse数据导入导出的集成测试。测试套件位于 `tests/integration/test_clickhouse_import_export.py`。

## 测试环境准备

### 1. 安装依赖

确保已安装所有必需的Python包：

```bash
# 使用uv创建虚拟环境
uv venv

# 激活虚拟环境
source .venv/bin/activate

# 同步依赖
uv pip sync requirements.txt

# 安装额外的测试依赖
uv pip install pytest-asyncio faker clickhouse-connect
```

### 2. 设置环境变量

```bash
export PYTHONPATH=/workspace/src:$PYTHONPATH
```

## 测试用例说明

### 基础导入导出测试
- **测试名称**: `test_basic_export_import_roundtrip`
- **功能**: 验证基础的数据导出和导入流程
- **涵盖内容**:
  - 创建测试表并插入100条数据
  - 导出数据到S3存储
  - 从S3导入数据到新表
  - 验证数据完整性和一致性

### 数据类型兼容性测试
- **测试名称**: `test_comprehensive_data_types_compatibility`
- **功能**: 测试所有支持的ClickHouse数据类型
- **涵盖类型**:
  - 数值类型: UInt8/16/32/64, Int8/16/32/64, Float32/64, Decimal
  - 字符串类型: String, FixedString
  - 日期时间类型: Date, DateTime, DateTime64
  - 布尔类型: Bool
  - 复杂类型: Array, UUID, Enum

### 批量导出测试
- **测试名称**: `test_batch_export_large_dataset`
- **功能**: 测试大数据集的分批导出功能
- **验证内容**:
  - 不同批次大小的处理（50, 100, 200条记录）
  - 内存效率优化
  - 数据完整性验证

### 主键冲突处理测试
- **测试名称**: `test_primary_key_conflict_strategies`
- **功能**: 测试主键冲突的不同处理策略
- **策略**:
  - IGNORE: 保留现有数据
  - REPLACE: 使用ReplacingMergeTree特性替换数据

### 元数据提供者测试
- **测试名称**: `test_metadata_provider`
- **功能**: 测试ClickHouse元数据查询功能
- **验证内容**:
  - 表存在性检查
  - 列信息获取
  - 索引信息提取
  - 表注释获取

## 运行测试

### 运行所有ClickHouse测试

```bash
pytest tests/integration/test_clickhouse_import_export.py -v -s
```

### 运行特定测试

```bash
# 运行基础导入导出测试
pytest tests/integration/test_clickhouse_import_export.py::TestClickHouseImportExport::test_basic_export_import_roundtrip -v -s

# 运行数据类型兼容性测试
pytest tests/integration/test_clickhouse_import_export.py::TestClickHouseImportExport::test_comprehensive_data_types_compatibility -v -s

# 运行批量导出测试
pytest tests/integration/test_clickhouse_import_export.py::TestClickHouseImportExport::test_batch_export_large_dataset -v -s

# 运行主键冲突处理测试
pytest tests/integration/test_clickhouse_import_export.py::TestClickHouseImportExport::test_primary_key_conflict_strategies -v -s

# 运行元数据测试
pytest tests/integration/test_clickhouse_import_export.py::TestClickHouseImportExport::test_metadata_provider -v -s
```

### 使用标记运行测试

```bash
# 仅运行ClickHouse相关测试
pytest -m clickhouse -v -s

# 运行需要MinIO的ClickHouse测试
pytest -m "clickhouse and minio" -v -s

# 运行所有集成测试
pytest -m integration -v -s
```

## 测试数据生成

测试使用Faker库生成真实的测试数据，包括：
- 中文姓名和地址
- 随机数值和日期
- 各种格式的字符串数据
- 复杂的嵌套数据结构

## 注意事项

1. **Docker要求**: 测试需要Docker来运行ClickHouse和MinIO容器
2. **Python版本**: 建议使用Python 3.12+
3. **表引擎**: ClickHouse表必须预先创建，需指定合适的引擎类型（如MergeTree, ReplacingMergeTree）
4. **数据合并**: ReplacingMergeTree需要执行`OPTIMIZE TABLE ... FINAL`才能确保数据正确合并
5. **时区处理**: 时区处理依赖于ClickHouse服务器配置

## 故障排查

### 连接被拒绝错误
如果看到 "Connection refused" 错误，确保：
1. Docker服务正在运行
2. ClickHouse容器已启动
3. 端口8123未被占用

### 模块未找到错误
如果看到模块导入错误，确保：
1. 已激活虚拟环境
2. 已安装所有依赖
3. PYTHONPATH设置正确

### 测试超时
某些测试可能需要较长时间，特别是涉及大数据集的测试。可以使用pytest的超时选项：

```bash
pytest tests/integration/test_clickhouse_import_export.py --timeout=300 -v -s
```

## 性能优化建议

1. **批次大小**: 根据数据量调整`cursor_fetch_size`和`parquet_chunk_size`
2. **内存使用**: 对于大数据集，使用流式处理避免内存溢出
3. **并发处理**: ClickHouse支持并发写入，可以考虑并行导入多个数据块
4. **压缩传输**: 连接器默认启用了数据压缩以提高传输效率

## 扩展测试

可以通过以下方式扩展测试覆盖：
1. 添加更多数据类型测试（如Nested, LowCardinality等）
2. 测试不同的表引擎（如CollapsingMergeTree, VersionedCollapsingMergeTree）
3. 添加性能基准测试
4. 测试分布式表的导入导出
5. 添加错误处理和异常场景测试