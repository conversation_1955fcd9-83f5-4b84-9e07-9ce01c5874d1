# PostgreSQL 集成测试功能说明

## 概述

本文档详细说明了 PostgreSQL 数据导入导出集成测试套件的功能和使用方法。测试套件位于 `tests/integration/test_postgres_import_export.py`，提供了全面的 PostgreSQL 连接器测试覆盖，包括数据类型兼容性、时区处理、分批导出、冲突处理等关键场景。

### 技术特性
- **Python 3.9+ 支持**: 使用内置泛型类型，无需 typing 模块导入
- **异步架构**: 基于 asyncio 和 pytest-asyncio 的异步测试框架
- **容器化测试**: 使用 testcontainers 自动管理 PostgreSQL 和 MinIO 容器
- **代码质量**: 所有函数认知复杂度低于 15，遵循最佳实践

## 核心测试功能

### 1. 基础导出导入测试 (`test_basic_export_import_roundtrip`)

**测试目标**: 验证基本的数据导出导入往返流程

**测试步骤**:
1. 创建包含基础数据类型的测试表
2. 插入 100 条测试数据（使用 Faker 生成真实数据）
3. 执行导出流程到 S3 存储
4. 执行导入流程到新表
5. 验证数据完整性和一致性

**数据类型覆盖**:
- 整数类型: `id`, `age`
- 字符串类型: `name`, `email`
- 布尔类型: `is_active`
- 时间戳类型: `created_at`

### 2. 综合数据类型兼容性测试 (`test_comprehensive_data_types_compatibility`)

**测试目标**: 验证各种 PostgreSQL 数据类型的导入导出兼容性

**支持的数据类型**:
- **数值类型**: SMALLINT, BIGINT, DECIMAL, REAL, DOUBLE PRECISION
- **字符串类型**: VARCHAR, TEXT, CHAR
- **日期时间类型**: DATE, TIME, TIMESTAMP, TIMESTAMPTZ
- **布尔类型**: BOOLEAN
- **二进制类型**: BYTEA
- **JSON 类型**: JSON, JSONB
- **数组类型**: INTEGER[], TEXT[]
- **UUID 类型**: UUID (支持自动类型转换)

**特殊处理**:
- UUID 类型转换: asyncpg UUID 对象 → 标准字符串格式
- 时区感知时间戳的 UTC 转换和比较
- JSON/JSONB 数据的序列化和反序列化
- 数组类型的格式标准化
- CHAR 类型尾随空格的处理

### 3. 时区处理综合测试 (`test_timezone_handling_comprehensive`)

**测试目标**: 验证时区相关数据的正确处理

**测试场景**:
- TIMESTAMP WITH TIME ZONE 字段的处理
- 不同时区数据的导入导出准确性
- UTC 时间与本地时间的转换
- 时区信息的保持和验证

**时区测试数据**:
- UTC 时间戳
- 本地时间戳
- 不同时区的时间数据
- 时区偏移量验证（允许 1 秒误差）

### 4. 大数据集批处理测试 (`test_batch_export_large_dataset`)

**测试目标**: 验证大数据集的分批处理能力

**测试内容**:
- 生成 1000 条测试数据
- 分批导出为多个 Parquet 文件
- 验证批处理的数据完整性
- 测试流式处理性能

**批处理特性**:
- 可配置的批处理大小
- 内存效率的流式处理
- 多文件输出支持

### 5. 主键冲突策略测试 (`test_primary_key_conflict_strategies`)

**测试目标**: 验证不同主键冲突处理策略

**冲突策略**:
- **IGNORE**: 忽略冲突记录，保留原有数据
- **REPLACE**: 替换冲突记录为新数据
- **UPSERT**: 更新冲突记录，插入新记录

**测试流程**:
1. 插入基础数据集（20 条记录）
2. 准备包含冲突的数据集（5 条冲突记录）
3. 使用不同策略导入冲突数据
4. 验证每种策略的处理结果

### 6. 复合主键冲突处理测试 (`test_composite_key_conflict_handling`)

**测试目标**: 验证联合主键场景下的冲突处理

**复合主键结构**:
- 多字段组合主键 (`user_id`, `product_id`)
- 复杂的冲突检测逻辑
- 联合主键的 UPSERT 操作

**测试验证**:
- 联合主键约束的正确执行
- 部分字段匹配的冲突检测
- 复合主键场景下的数据完整性

## 辅助功能和工具方法

### 数据生成器

#### 基础数据生成 (`_generate_basic_test_data`)
- 使用 Faker 库生成真实测试数据
- 支持中文 locale (`zh_CN`)
- 可配置数据量（默认 100 条）

#### 综合数据类型生成 (`_generate_comprehensive_test_data`)
- 覆盖所有支持的 PostgreSQL 数据类型
- 包含边界值和特殊值测试
- JSON 和数组类型的复杂数据结构

#### 冲突数据生成 (`_generate_conflict_test_data`)
- 生成基础数据集和冲突数据集
- 可配置冲突比例
- 支持单一主键和复合主键场景

#### 时区测试数据生成 (`_generate_timezone_test_data`)
- 生成不同时区的时间戳数据
- UTC 和本地时间的混合数据
- 时区偏移量测试数据

### 数据验证器（优化后的低复杂度函数）

#### 数值字段比较 (`_compare_numeric_fields`)
- **主函数**: 遍历数值字段并调用单字段比较
- **辅助函数**:
  - `_compare_single_numeric_field`: 比较单个数值字段
  - `_is_decimal_field`: 检查是否为 Decimal 字段
  - `_is_float_field`: 检查是否为 float 字段
  - `_compare_decimal_values`: Decimal 类型的精度比较
  - `_compare_float_values`: 浮点数的容差比较
  - `_compare_exact_values`: 整数类型的精确比较

#### 数组字段比较 (`_compare_array_fields`)
- **主函数**: 遍历数组字段并调用单字段比较
- **辅助函数**:
  - `_compare_single_array_field`: 比较单个数组字段
  - `_normalize_array_value`: 标准化数组格式
  - `_assert_arrays_equal`: 断言数组相等

#### 其他比较器
- **字符串字段比较**: CHAR 类型的尾随空格处理
- **日期时间字段比较**: 时区感知时间戳的 UTC 转换比较
- **JSON 字段比较**: JSON 和 JSONB 数据的深度比较
- **二进制字段比较**: BYTEA 类型的二进制数据验证

### 表结构管理

#### 基础表结构 (`_create_basic_test_table_schema`)
- 包含常用数据类型的表结构
- 主键和索引定义
- 约束条件设置

#### 综合表结构 (`_create_comprehensive_test_table_schema`)
- 覆盖所有支持数据类型的完整表结构
- 复杂约束和关系定义
- 性能优化的索引策略

#### 时区测试表结构 (`_create_timezone_test_table_schema`)
- 专门用于时区测试的表结构
- TIMESTAMPTZ 字段定义
- 时区相关约束

### 工具方法

#### 表名生成 (`_generate_unique_table_name`)
- 生成唯一的测试表名
- 避免测试间的表名冲突
- 支持前缀自定义

#### S3 键生成 (`_create_s3_key`)
- 生成唯一的 S3 对象键
- 层次化的键结构
- 测试隔离保证

#### 数据操作
- **数据插入** (`_insert_test_data`): 批量数据插入优化
- **数据获取** (`_fetch_table_data`): 支持排序的数据获取
- **表行计数** (`_count_table_rows`): 高效的行数统计
- **数据重置** (`_reset_table_data`): 测试数据的重置和恢复

## 测试环境要求

### 依赖服务
- **PostgreSQL**: 使用 testcontainers 自动启动 PostgreSQL 容器
- **MinIO**: 使用 testcontainers 自动启动 MinIO 容器作为 S3 兼容存储
- **Python 环境**: Python 3.9+ 支持内置泛型类型

### 测试标记
所有测试都使用以下 pytest 标记：
- `@pytest.mark.postgres`: PostgreSQL 相关测试
- `@pytest.mark.minio`: MinIO S3 存储相关测试
- `@pytest.mark.integration`: 集成测试标记
- `@pytest.mark.asyncio`: 异步测试标记

### 依赖项
- Python 3.9+
- pytest
- pytest-asyncio
- testcontainers
- faker
- PostgreSQL 容器
- MinIO 容器
- 现有的 datax 包依赖

### 容器服务
测试套件依赖以下容器服务（通过 testcontainers 自动管理）：
- PostgreSQL 13+
- MinIO（S3 兼容存储）

## 运行测试

### 环境设置
```bash
# 设置Python路径
export PYTHONPATH=src:tests

# 确保已安装依赖
pip install faker pytest-html pytest-cov
```

### 运行完整测试套件
```bash
# 运行所有PostgreSQL集成测试
pytest tests/integration/test_postgres_import_export.py -v

# 运行特定标记的测试
pytest tests/integration/test_postgres_import_export.py -m "postgres and minio" -v

# 运行单个测试
pytest tests/integration/test_postgres_import_export.py::TestPostgresImportExport::test_basic_export_import_roundtrip -v
```

### 测试标记
- `@pytest.mark.postgres`: PostgreSQL相关测试
- `@pytest.mark.minio`: MinIO/S3相关测试
- `@pytest.mark.integration`: 集成测试
- `@pytest.mark.asyncio`: 异步测试

### 环境变量配置
```bash
# 可选：设置日志级别
export LOG_LEVEL=DEBUG

# 可选：设置测试超时
export PYTEST_TIMEOUT=300
```

## 测试数据

### 测试数据生成
测试套件使用 Faker 库生成真实的测试数据：
- 中文姓名和地址（locale: `zh_CN`）
- 真实的电子邮件格式
- 合理的数值范围
- 多样化的时区数据
- 复杂的 JSON 结构
- 多维数组数据

### 数据量配置
- **基础测试**: 100 条记录
- **综合数据类型测试**: 50 条记录（复杂数据类型）
- **时区测试**: 25 条记录
- **大数据集测试**: 1000 条记录
- **冲突处理测试**: 20 条基础 + 5 条冲突记录
- **复合主键测试**: 15 条基础 + 3 条冲突记录

### 覆盖的业务场景
1. **日常数据同步**: 基础数据类型的常规导入导出
2. **复杂数据迁移**: 包含各种 PostgreSQL 特殊类型的数据迁移
3. **大数据处理**: 大规模数据集的批处理和流式处理
4. **数据冲突处理**: 实际业务中的数据冲突和去重场景
5. **国际化支持**: 多时区环境下的时间数据处理
6. **JSON 数据处理**: 复杂 JSON 结构的存储和检索
7. **数组数据处理**: 多维数组数据的完整性验证

## 故障排除

### 常见问题

1. **容器启动失败**
   ```
   解决方案：确保Docker服务正在运行，端口未被占用
   ```

2. **测试超时**
   ```
   解决方案：增加pytest超时设置或检查网络连接
   ```

3. **数据类型不匹配**
   ```
   解决方案：检查PostgreSQL版本兼容性和数据类型映射
   ```

4. **S3连接失败**
   ```
   解决方案：验证MinIO容器状态和网络配置
   ```

5. **导入路径错误**
   ```
   解决方案：确保设置了正确的PYTHONPATH=src:tests
   ```

### 调试技巧

1. **启用详细日志**
   ```bash
   pytest tests/integration/test_postgres_import_export.py -v -s --log-cli-level=DEBUG
   ```

2. **保留测试数据**
   ```python
   # 在测试方法中添加断点或sleep来检查中间状态
   import time
   time.sleep(60)  # 保持容器运行60秒
   ```

3. **单独运行失败的测试**
   ```bash
   pytest tests/integration/test_postgres_import_export.py::TestPostgresImportExport::test_specific_method -v -s
   ```

## 扩展测试

### 添加新的数据类型测试
1. 在`_create_comprehensive_data_types_table_schema`中添加新字段
2. 在`_generate_comprehensive_test_data`中添加数据生成逻辑
3. 在`_compare_comprehensive_data_types`中添加比较逻辑

### 添加新的冲突策略测试
1. 在`test_primary_key_conflict_strategies`中添加新策略
2. 实现相应的验证逻辑
3. 更新`_test_conflict_strategy`方法

### 性能测试扩展
1. 增加测试数据量
2. 添加性能指标收集
3. 实现基准测试比较

## 测试报告

### 生成HTML报告
```bash
pip install pytest-html
pytest tests/integration/test_postgres_import_export.py --html=report.html --self-contained-html
```

### 生成覆盖率报告
```bash
pip install pytest-cov
pytest tests/integration/test_postgres_import_export.py --cov=src/datax --cov-report=html
```

## 维护指南

### 定期维护任务
1. 更新测试数据以反映真实场景
2. 验证新PostgreSQL版本的兼容性
3. 更新依赖项版本
4. 优化测试执行时间

### 代码质量
- **认知复杂度控制**: 所有函数复杂度低于 15
- **类型注解**: 使用 Python 3.9+ 内置泛型类型
- **函数分解**: 复杂逻辑拆分为专门的辅助函数
- **文档字符串**: 详细的中文文档说明
- **异步编程**: 遵循 asyncio 最佳实践

### 性能和可靠性

#### 性能特性
- **流式处理**: 大数据集的内存效率处理
- **批处理优化**: 可配置的批处理大小
- **连接池管理**: 高效的数据库连接复用
- **并发安全**: 多测试并行执行的安全保证

#### 可靠性保证
- **事务安全**: 所有数据操作都在事务中执行
- **资源清理**: 自动清理测试数据和临时资源
- **错误恢复**: 完善的错误处理和恢复机制
- **数据一致性**: 严格的数据验证和完整性检查

### 错误场景覆盖
- 连接失败和重试机制
- 数据类型不匹配处理
- 约束违反和冲突解决
- 内存不足和资源限制
- 网络中断和恢复

## 扩展和维护

### 添加新的数据类型测试
1. 在 `_create_comprehensive_test_table_schema` 中添加新字段
2. 在 `_generate_comprehensive_test_data` 中生成测试数据
3. 在相应的比较方法中添加验证逻辑

### 添加新的冲突策略
1. 在 `ConflictStrategy` 枚举中添加新策略
2. 在冲突测试中添加新策略的测试用例
3. 更新验证逻辑以支持新策略

### 性能优化建议
1. 根据测试需求调整数据量大小
2. 优化批处理大小以平衡内存和性能
3. 使用适当的索引策略提高查询性能
4. 监控测试执行时间并进行优化

---

**注意**: 本测试套件设计为在 CI/CD 环境中运行，所有外部依赖都通过容器自动管理，无需手动配置数据库或存储服务。测试套件经过函数复杂度优化，所有函数都遵循单一职责原则，认知复杂度控制在 15 以下，确保代码的可维护性和可读性。
