# 精卫 - 数据同步、拆分、迁移系统

## 目录结构

```text
├──.venv/                      # 由 uv 管理的虚拟环境
├── docs/                       # 项目文档 (例如，架构决策记录、API 文档)
├── scripts/                    # 辅助脚本 (例如，部署、数据初始化)
│   └── apply_s3_lifecycle.py   # 新增：用于应用S3生命周期策略的脚本
├── configs/                    # 新增：存放配置文件的目录
│   └── s3_lifecycle.yml        # 新增：S3生命周期策略配置文件
├── src/                        # 应用主源码目录
│   └── datax/          # Python 包
│       ├── __init__.py
│       ├── core/               # 核心抽象，如 Extractor, Translator, Loader
│       ├── connectors/         # 具体的数据库连接器实现
│       ├── flows/              # Prefect 工作流定义
│       ├── models/             # Pydantic 配置模型
│       └── utils/              # 通用工具函数
├── tests/                      # 测试代码
│   ├── __init__.py
│   ├── conftest.py             # Pytest 配置文件和全局 fixtures
│   ├── integration/            # 集成测试
│   └── unit/                   # 单元测试
├── CLAUDE.md                   # 项目说明
├── AGENTS.md                   # 操作指令
├── pyproject.toml              # 项目配置的唯一真实来源
└── requirements.txt            # 由 uv 生成的锁定依赖文件
```
