#!/usr/bin/env python3

"""
数据处理核心模块。

提供数据提取、加载、转换的抽象接口和工厂函数。
"""

from .extractor import Extractor
from .factories import get_extractor, get_loader, get_translator, get_translators
from .loader import Loader
from .metadata import CachedMetadataProvider, MetadataCache, MetadataProvider
from .translator import (
    AsyncTranslatorExample,
    NoOpTranslator,
    Translator,
    chain_translators,
)

__all__ = [
    # 抽象基类
    "Extractor",
    "Loader",
    "Translator",
    "MetadataProvider",
    # 具体实现
    "NoOpTranslator",
    "AsyncTranslatorExample",
    "CachedMetadataProvider",
    "MetadataCache",
    # 工厂函数
    "get_extractor",
    "get_loader",
    "get_translator",
    "get_translators",
    # 辅助函数
    "chain_translators",
]
