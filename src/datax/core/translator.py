#!/usr/bin/env python3

"""
数据转换器抽象基类模块。

本模块定义了数据转换器的抽象接口，用于在数据提取和加载之间进行数据转换处理。
转换器采用流式处理设计，可以对数据流进行实时转换而不需要将所有数据加载到内存。
默认提供了一个 NoOpTranslator 实现，可以直接传递数据而不做任何转换。

主要功能：
- 定义标准的数据转换器接口
- 支持流式数据转换
- 提供默认的透传转换器实现
- 支持自定义转换逻辑扩展
"""

from abc import ABC, abstractmethod
from collections.abc import AsyncIterator

import pandas as pd

from ..models import TranslatorConfig


class Translator(ABC):
    """数据转换器接口定义"""

    @abstractmethod
    def transform_stream(
        self,
        data_stream: AsyncIterator[pd.DataFrame],
        translator_config: TranslatorConfig | None = None,
    ) -> AsyncIterator[pd.DataFrame]:
        """
        对输入的数据流（DataFrame块）进行转换。

        :param data_stream: 一个迭代器，每次产出一个 pandas DataFrame。
        :yield: 转换后的 pandas DataFrame 数据块。
        """
        raise NotImplementedError


class NoOpTranslator(Translator):
    """一个默认的、不执行任何操作的转换器。"""

    def transform_stream(
        self,
        data_stream: AsyncIterator[pd.DataFrame],
        translator_config: TranslatorConfig | None = None,
    ) -> AsyncIterator[pd.DataFrame]:
        """直接将输入流原样返回。"""
        _ = translator_config  # NoOpTranslator 不使用配置参数
        return data_stream


async def chain_translators(
    data_stream: AsyncIterator[pd.DataFrame],
    translators: list[Translator],
) -> AsyncIterator[pd.DataFrame]:
    """
    将多个转换器链式连接，上一个转换器的输出作为下一个转换器的输入。

    参数:
        data_stream: 输入数据流
        translators: 转换器列表，按顺序应用

    返回:
        AsyncIterator[pd.DataFrame]: 经过所有转换器处理后的数据流
    """
    if not translators:
        # 如果没有转换器，直接返回原始数据流
        async for chunk in data_stream:
            yield chunk
        return

    # 从第一个转换器开始，逐个应用转换
    current_stream = data_stream
    for translator in translators:
        current_stream = translator.transform_stream(current_stream)

    # 返回最终的转换结果
    async for chunk in current_stream:
        yield chunk
