#!/usr/bin/env python3

"""
数据转换器抽象基类模块。

本模块定义了数据转换器的抽象接口，用于在数据提取和加载之间进行数据转换处理。
转换器采用流式处理设计，可以对数据流进行实时转换而不需要将所有数据加载到内存。
默认提供了一个 NoOpTranslator 实现，可以直接传递数据而不做任何转换。

主要功能：
- 定义标准的数据转换器接口
- 支持流式数据转换
- 提供默认的透传转换器实现
- 支持自定义转换逻辑扩展
"""

from abc import ABC, abstractmethod
from collections.abc import AsyncIterator

import pandas as pd

from ..models import TranslatorConfig


class Translator(ABC):
    """数据转换器接口定义"""

    @abstractmethod
    def transform_stream(
        self,
        data_stream: AsyncIterator[pd.DataFrame],
        translator_config: TranslatorConfig | None = None,
    ) -> AsyncIterator[pd.DataFrame]:
        """
        对输入的数据流（DataFrame块）进行转换。

        这是一个返回异步迭代器的方法，用于处理数据流的转换。
        实现时应该返回一个异步生成器。

        :param data_stream: 一个异步迭代器，每次产出一个 pandas DataFrame。
        :param translator_config: 可选的转换器配置参数。
        :return: 转换后的异步数据流迭代器。
        """
        raise NotImplementedError


class NoOpTranslator(Translator):
    """一个默认的、不执行任何操作的转换器。"""

    def transform_stream(
        self,
        data_stream: AsyncIterator[pd.DataFrame],
        translator_config: TranslatorConfig | None = None,
    ) -> AsyncIterator[pd.DataFrame]:
        """直接将输入流原样返回。"""
        _ = translator_config  # NoOpTranslator 不使用配置参数
        return data_stream


class AsyncTranslatorExample(Translator):
    """一个示例异步转换器，展示如何实现异步数据转换。"""

    def transform_stream(
        self,
        data_stream: AsyncIterator[pd.DataFrame],
        translator_config: TranslatorConfig | None = None,
    ) -> AsyncIterator[pd.DataFrame]:
        """异步转换数据流的示例实现。"""
        return self._async_transform(data_stream, translator_config)

    async def _async_transform(
        self,
        data_stream: AsyncIterator[pd.DataFrame],
        translator_config: TranslatorConfig | None = None,
    ) -> AsyncIterator[pd.DataFrame]:
        """内部异步生成器方法。"""
        _ = translator_config  # 示例中不使用配置参数

        async for df_chunk in data_stream:
            # 这里可以进行异步的数据转换操作
            # 例如：异步数据库查询、API调用等

            # 示例：添加一个处理时间戳列
            import asyncio

            await asyncio.sleep(0.001)  # 模拟异步操作

            transformed_df = df_chunk.copy()
            transformed_df["processed_at"] = pd.Timestamp.now()

            yield transformed_df


async def chain_translators(
    data_stream: AsyncIterator[pd.DataFrame],
    translators: list[Translator],
) -> AsyncIterator[pd.DataFrame]:
    """
    将多个转换器链式连接，上一个转换器的输出作为下一个转换器的输入。

    这个函数支持异步工作模式，每个转换器都可以执行异步操作（如数据库查询、API调用等）。
    转换器按照列表中的顺序依次处理数据流，形成一个处理管道。

    参数:
        data_stream: 输入的异步数据流
        translators: 转换器列表，按顺序应用。每个转换器都应该实现异步的 transform_stream 方法

    返回:
        AsyncIterator[pd.DataFrame]: 经过所有转换器处理后的异步数据流

    示例:
        ```python
        # 创建转换器链
        translators = [
            ColumnRenameTranslator(),
            DataValidationTranslator(),
            AsyncEnrichmentTranslator(),
        ]

        # 应用转换器链
        transformed_stream = chain_translators(raw_data_stream, translators)

        # 处理转换后的数据
        async for df_chunk in transformed_stream:
            # 处理每个数据块
            process_chunk(df_chunk)
        ```
    """
    if not translators:
        # 如果没有转换器，直接返回原始数据流
        async for chunk in data_stream:
            yield chunk
        return

    # 从第一个转换器开始，逐个应用转换
    # 每个转换器的输出成为下一个转换器的输入
    current_stream = data_stream
    for translator in translators:
        current_stream = translator.transform_stream(current_stream)

    # 异步迭代最终的转换结果
    async for chunk in current_stream:
        yield chunk
