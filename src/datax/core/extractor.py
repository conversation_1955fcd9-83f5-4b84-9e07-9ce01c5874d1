#!/usr/bin/env python3

"""
数据提取器抽象基类模块。

本模块定义了数据提取器的抽象接口，所有具体的数据库提取器都必须继承并实现该接口。
核心设计采用流式处理模式，支持处理大规模数据集而不会耗尽内存。

主要功能：
- 定义标准的数据提取器接口
- 实现流式数据提取机制
"""

from abc import ABC, abstractmethod
from collections.abc import AsyncIterator

import pandas as pd

from ..models import ExportConfig, QueryConfig


class Extractor(ABC):
    """数据提取器接口定义。"""

    @abstractmethod
    async def extract_stream(
        self, query_config: QueryConfig, export_config: ExportConfig
    ) -> AsyncIterator[pd.DataFrame]:
        """
        核心方法，以流式方式提取数据并产生 pandas DataFrame 块。

        此方法必须实现为生成器，逐块（chunk）产生 pandas DataFrame。
        这种设计对于处理亿级记录至关重要，因为它避免了将整个数据集加载到内存中。

        :param query_config: 包含查询条件（如 SQL、范围、字段列表）的 Pydantic 模型。
        :param export_config: 包含批次大小等导出配置的 Pydantic 模型。
        :yield: pandas DataFrame 数据块。
        """
        raise NotImplementedError
