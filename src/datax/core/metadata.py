#!/usr/bin/env python3

"""
元数据查询核心模块。

本模块定义了数据库元数据查询的抽象接口和缓存机制。
提供统一的接口来查询不同数据库的表结构、索引、约束等元数据信息。

主要组件：
- MetadataProvider: 元数据查询抽象接口
- MetadataCache: 元数据缓存管理器
"""

import logging
from abc import ABC, abstractmethod

from ..models.metadata import TableMetadata

logger = logging.getLogger(__name__)


class MetadataProvider(ABC):
    """数据库元数据查询抽象接口"""

    @abstractmethod
    async def get_table_metadata(
        self, table_name: str, schema_name: str | None = None
    ) -> TableMetadata:
        """
        获取表的元数据信息。

        参数:
            table_name: 表名
            schema_name: 模式名（可选，用于PostgreSQL等数据库）

        返回:
            TableMetadata: 表元数据信息

        异常:
            ValueError: 表不存在时抛出
            Exception: 查询失败时抛出
        """
        pass

    @abstractmethod
    async def table_exists(
        self, table_name: str, schema_name: str | None = None
    ) -> bool:
        """
        检查表是否存在。

        参数:
            table_name: 表名
            schema_name: 模式名（可选）

        返回:
            bool: 表是否存在
        """
        pass

    @abstractmethod
    async def get_database_name(self) -> str:
        """
        获取当前连接的数据库名称。

        返回:
            str: 数据库名称
        """
        pass


class MetadataCache:
    """元数据缓存管理器"""

    def __init__(self, max_size: int = 100):
        """
        初始化元数据缓存。

        参数:
            max_size: 缓存的最大条目数
        """
        self._cache: dict[str, TableMetadata] = {}
        self._max_size = max_size
        self._access_order: list[str] = []

    def _make_cache_key(
        self,
        table_name: str,
        schema_name: str | None = None,
        database_name: str | None = None,
    ) -> str:
        """生成缓存键"""
        parts = []
        if database_name:
            parts.append(database_name)
        if schema_name:
            parts.append(schema_name)
        parts.append(table_name)
        return ".".join(parts)

    def get(
        self,
        table_name: str,
        schema_name: str | None = None,
        database_name: str | None = None,
    ) -> TableMetadata | None:
        """
        从缓存中获取表元数据。

        参数:
            table_name: 表名
            schema_name: 模式名
            database_name: 数据库名

        返回:
            TableMetadata | None: 缓存的元数据或None
        """
        cache_key = self._make_cache_key(table_name, schema_name, database_name)

        if cache_key in self._cache:
            # 更新访问顺序（LRU）
            self._access_order.remove(cache_key)
            self._access_order.append(cache_key)
            logger.debug(f"Cache hit for table metadata: {cache_key}")
            return self._cache[cache_key]

        logger.debug(f"Cache miss for table metadata: {cache_key}")
        return None

    def put(
        self,
        metadata: TableMetadata,
        schema_name: str | None = None,
        database_name: str | None = None,
    ) -> None:
        """
        将表元数据放入缓存。

        参数:
            metadata: 表元数据
            schema_name: 模式名
            database_name: 数据库名
        """
        cache_key = self._make_cache_key(
            metadata.table_name, schema_name, database_name
        )

        # 如果缓存已满，移除最久未使用的条目
        if len(self._cache) >= self._max_size and cache_key not in self._cache:
            oldest_key = self._access_order.pop(0)
            del self._cache[oldest_key]
            logger.debug(f"Evicted cache entry: {oldest_key}")

        # 添加或更新缓存
        if cache_key in self._cache:
            self._access_order.remove(cache_key)

        self._cache[cache_key] = metadata
        self._access_order.append(cache_key)
        logger.debug(f"Cached table metadata: {cache_key}")

    def clear(self) -> None:
        """清空缓存"""
        self._cache.clear()
        self._access_order.clear()
        logger.debug("Metadata cache cleared")

    def remove(
        self,
        table_name: str,
        schema_name: str | None = None,
        database_name: str | None = None,
    ) -> bool:
        """
        从缓存中移除指定表的元数据。

        参数:
            table_name: 表名
            schema_name: 模式名
            database_name: 数据库名

        返回:
            bool: 是否成功移除
        """
        cache_key = self._make_cache_key(table_name, schema_name, database_name)

        if cache_key in self._cache:
            del self._cache[cache_key]
            self._access_order.remove(cache_key)
            logger.debug(f"Removed cache entry: {cache_key}")
            return True

        return False

    def size(self) -> int:
        """获取缓存大小"""
        return len(self._cache)


class CachedMetadataProvider:
    """带缓存的元数据查询器"""

    def __init__(self, provider: MetadataProvider, cache_size: int = 100):
        """
        初始化带缓存的元数据查询器。

        参数:
            provider: 底层元数据查询提供者
            cache_size: 缓存大小
        """
        self.provider = provider
        self.cache = MetadataCache(cache_size)

    async def get_table_metadata(
        self, table_name: str, schema_name: str | None = None
    ) -> TableMetadata:
        """
        获取表元数据（带缓存）。

        参数:
            table_name: 表名
            schema_name: 模式名

        返回:
            TableMetadata: 表元数据
        """
        # 获取数据库名用于缓存键
        database_name = await self.provider.get_database_name()

        # 尝试从缓存获取
        cached_metadata = self.cache.get(table_name, schema_name, database_name)
        if cached_metadata:
            return cached_metadata

        # 缓存未命中，从数据库查询
        metadata = await self.provider.get_table_metadata(table_name, schema_name)

        # 放入缓存
        self.cache.put(metadata, schema_name, database_name)

        return metadata

    async def table_exists(
        self, table_name: str, schema_name: str | None = None
    ) -> bool:
        """检查表是否存在"""
        return await self.provider.table_exists(table_name, schema_name)

    def clear_cache(self) -> None:
        """清空缓存"""
        self.cache.clear()

    def remove_from_cache(
        self, table_name: str, schema_name: str | None = None
    ) -> bool:
        """从缓存中移除指定表"""
        database_name = None  # 这里可能需要异步获取，暂时使用None
        return self.cache.remove(table_name, schema_name, database_name)
