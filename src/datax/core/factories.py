#!/usr/bin/env python3

"""
工厂方法模块。

本模块提供了创建提取器、加载器和转换器实例的工厂方法。
根据配置中的数据源类型，动态导入并创建相应的具体实现类。
采用延迟导入策略，只在需要时才导入具体的连接器模块。

主要功能：
- 根据数据源类型创建相应的提取器实例
- 根据数据源类型创建相应的加载器实例
- 根据配置创建转换器实例
- 支持多种数据库类型（MySQL、PostgreSQL、ClickHouse、MongoDB）
- 提供统一的创建接口，隐藏具体实现细节
"""

import logging
from typing import cast

from ..models import (
    AnyDatabaseConfig,
    ClickHouseConfig,
    DatasourceKind,
    MongoConfig,
    MySQLConfig,
    PostgresConfig,
    TranslatorConfig,
)
from .extractor import Extractor
from .loader import Loader
from .translator import NoOpTranslator, Translator

logger = logging.getLogger(__name__)


def get_extractor(config: AnyDatabaseConfig) -> Extractor:
    """
    创建并返回相应的提取器实例。

    根据配置中的数据源类型，动态导入并创建对应的提取器。
    支持 MySQL、PostgreSQL、ClickHouse 和 MongoDB。

    参数:
        config: 数据库配置对象，包含数据源类型和连接信息

    返回:
        Extractor: 对应数据源类型的提取器实例

    异常:
        ValueError: 当数据源类型不被支持时抛出
    """
    logger.info(f"Creating extractor for kind: {config.kind}")
    match config.kind:
        case DatasourceKind.POSTGRES:
            from datax.connectors.postgres import PostgresExtractor

            return PostgresExtractor(cast(PostgresConfig, config))
        case DatasourceKind.MYSQL:
            from datax.connectors.mysql import MySQLExtractor

            return MySQLExtractor(cast(MySQLConfig, config))
        case DatasourceKind.CLICKHOUSE:
            from datax.connectors.clickhouse import ClickHouseExtractor

            return ClickHouseExtractor(cast(ClickHouseConfig, config))
        case DatasourceKind.MONGODB:
            from datax.connectors.mongo import MongoExtractor

            return MongoExtractor(cast(MongoConfig, config))
        case _:
            raise ValueError(
                f"Unsupported database kind for extractor: '{config.kind}'. Supported kinds: {[DatasourceKind.MYSQL, DatasourceKind.POSTGRES, DatasourceKind.CLICKHOUSE, DatasourceKind.MONGODB]}"
            )


def get_loader(config: AnyDatabaseConfig) -> Loader:
    """
    创建并返回相应的加载器实例。

    根据配置中的数据源类型，动态导入并创建对应的加载器。
    支持 MySQL、PostgreSQL、ClickHouse 和 MongoDB。

    参数:
        config: 数据库配置对象，包含数据源类型和连接信息

    返回:
        Loader: 对应数据源类型的加载器实例

    异常:
        ValueError: 当数据源类型不被支持时抛出
    """
    logger.info(f"Creating loader for kind: {config.kind}")
    match config.kind:
        case DatasourceKind.POSTGRES:
            from datax.connectors.postgres import PostgresLoader

            return PostgresLoader(cast(PostgresConfig, config))
        case DatasourceKind.MYSQL:
            from datax.connectors.mysql import MySQLLoader

            return MySQLLoader(cast(MySQLConfig, config))
        case DatasourceKind.CLICKHOUSE:
            from datax.connectors.clickhouse import ClickHouseLoader

            return ClickHouseLoader(cast(ClickHouseConfig, config))
        case DatasourceKind.MONGODB:
            from datax.connectors.mongo import MongoLoader

            return MongoLoader(cast(MongoConfig, config))
        case _:
            raise ValueError(
                f"Unsupported database kind for loader: '{config.kind}'. Supported kinds: {[DatasourceKind.MYSQL, DatasourceKind.POSTGRES, DatasourceKind.CLICKHOUSE, DatasourceKind.MONGODB]}"
            )


def get_translator(config: TranslatorConfig | None) -> Translator:
    """
    创建并返回转换器实例。

    根据配置创建相应的转换器。如果没有提供配置，则返回默认的 NoOpTranslator。

    参数:
        config: 转换器配置对象，可以为 None

    返回:
        Translator: 转换器实例

    异常:
        ValueError: 当转换器类型不被支持时抛出
    """
    if not config:
        logger.info("No translator config provided, using NoOpTranslator.")
        return NoOpTranslator()

    logger.info(f"Creating translator for kind: {config.kind}")
    match config.kind:
        case "noop":
            return NoOpTranslator()
        case _:
            raise ValueError(f"Unsupported translator kind: '{config.kind}'")


def get_translators(configs: list[TranslatorConfig] | None) -> list[Translator]:
    """
    创建并返回多个转换器实例的列表。

    根据配置列表创建相应的转换器。如果没有提供配置，则返回包含一个 NoOpTranslator 的列表。

    参数:
        configs: 转换器配置对象列表，可以为 None

    返回:
        list[Translator]: 转换器实例列表

    异常:
        ValueError: 当转换器类型不被支持时抛出
    """
    if not configs:
        logger.info("No translator configs provided, using single NoOpTranslator.")
        return [NoOpTranslator()]

    translators = []
    for i, config in enumerate(configs):
        logger.info(
            f"Creating translator {i + 1}/{len(configs)} for kind: {config.kind}"
        )
        translator = get_translator(config)
        translators.append(translator)

    logger.info(f"Created {len(translators)} translators for chaining.")
    return translators
