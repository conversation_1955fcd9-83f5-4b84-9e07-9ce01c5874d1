#!/usr/bin/env python3

"""
S3 工具模块。

本模块提供了与 Amazon S3 交互的异步工具函数和类。
支持文件上传、下载、列表、删除等操作，并提供了批量操作和生命周期策略管理功能。

主要功能：
- upload_file_to_s3: 异步上传单个文件到 S3
- download_file_from_s3: 异步从 S3 下载单个文件
- list_s3_objects: 异步列出 S3 对象
- delete_s3_object: 异步删除 S3 对象
- apply_s3_lifecycle_policy: 应用 S3 生命周期策略
- upload_multiple_files_to_s3: 批量上传文件到 S3
- AsyncS3Client: 异步 S3 客户端类，提供上下文管理器支持
"""

import asyncio
import logging
import sys
from collections.abc import AsyncGenerator
from types import TracebackType
from typing import Any, Self

import aioboto3
import yaml
from botocore.exceptions import ClientError
from boto3.s3.transfer import TransferConfig

from ..models import S3Config

logger = logging.getLogger(__name__)


async def upload_file_to_s3(local_path: str, s3_config: S3Config, s3_key: str) -> None:
    """
    异步上传单个文件到S3，支持并发控制和带宽限制。

    Args:
        local_path: 本地文件路径
        s3_config: S3配置对象
        s3_key: S3对象键名
    """
    session = aioboto3.Session()
    async with session.client("s3") as s3_client:  # type: ignore
        logger.info(
            f"Uploading {local_path} to s3://{s3_config.bucket}/{s3_key} "
            f"with max_concurrency={s3_config.max_concurrency} "
            f"and max_bandwidth={s3_config.max_bandwidth_mbps} MB/s."
        )
        try:
            config = TransferConfig(
                max_concurrency=s3_config.max_concurrency,
                max_bandwidth=s3_config.max_bandwidth_mbps * 1024 * 1024,
            )
            await s3_client.upload_file(
                local_path,
                s3_config.bucket,
                s3_key,
                Config=config,
            )
            logger.info(f"Successfully uploaded {s3_key}.")
        except ClientError as e:
            logger.error(f"Failed to upload {local_path} to S3: {e}")
            raise


async def download_file_from_s3(
    s3_config: S3Config, s3_key: str, local_path: str
) -> None:
    """
    异步从S3下载单个文件。

    Args:
        s3_config: S3配置对象
        s3_key: S3对象键名
        local_path: 本地保存路径
    """
    session = aioboto3.Session()
    async with session.client("s3") as s3_client:  # type: ignore
        logger.info(f"Downloading s3://{s3_config.bucket}/{s3_key} to {local_path}")
        try:
            config = TransferConfig(
                max_concurrency=s3_config.max_concurrency,
                max_bandwidth=s3_config.max_bandwidth_mbps * 1024 * 1024,
            )
            await s3_client.download_file(
                s3_config.bucket,
                s3_key,
                local_path,
                Config=config,
            )
            logger.info(f"Successfully downloaded {s3_key}.")
        except ClientError as e:
            logger.error(f"Failed to download {s3_key} from S3: {e}")
            raise


async def list_s3_objects(s3_config: S3Config, prefix: str = "") -> AsyncGenerator[str]:
    """
    异步列出S3桶中的对象。

    Args:
        s3_config: S3配置对象
        prefix: 对象前缀过滤器

    Yields:
        S3对象键名
    """
    session = aioboto3.Session()
    async with session.client("s3") as s3_client:  # type: ignore
        paginator = s3_client.get_paginator("list_objects_v2")
        async for page in paginator.paginate(Bucket=s3_config.bucket, Prefix=prefix):
            if "Contents" in page:
                for obj in page["Contents"]:
                    yield obj["Key"]


async def delete_s3_object(s3_config: S3Config, s3_key: str) -> None:
    """
    异步删除S3对象。

    Args:
        s3_config: S3配置对象
        s3_key: S3对象键名
    """
    session = aioboto3.Session()
    async with session.client("s3") as s3_client:  # type: ignore
        logger.info(f"Deleting s3://{s3_config.bucket}/{s3_key}")
        try:
            await s3_client.delete_object(Bucket=s3_config.bucket, Key=s3_key)
            logger.info(f"Successfully deleted {s3_key}.")
        except ClientError as e:
            logger.error(f"Failed to delete {s3_key} from S3: {e}")
            raise


async def apply_s3_lifecycle_policy(bucket_name: str, config_path: str) -> None:
    """
    异步加载YAML文件中的生命周期规则并应用到S3桶。
    这将覆盖桶上的任何现有生命周期配置。

    Args:
        bucket_name: S3桶名称
        config_path: 生命周期配置文件路径
    """
    try:
        with open(config_path, "rb") as f:
            config = yaml.safe_load(f)
    except FileNotFoundError:
        logger.error(f"Lifecycle config file not found at: {config_path}")
        sys.exit(1)
    except yaml.YAMLError as e:
        logger.error(f"Error parsing YAML file {config_path}: {e}")
        sys.exit(1)

    rules = []
    for rule_config in config.get("rules", []):
        rules.append(
            {
                "ID": rule_config["id"],
                "Filter": {"Prefix": rule_config["prefix"]},
                "Status": "Enabled",
                "Expiration": {"Days": rule_config["expiration_days"]},
            }
        )

    if not rules:
        logger.warning("No rules found in the lifecycle config file. Exiting.")
        return

    lifecycle_configuration = {"Rules": rules}
    session = aioboto3.Session()

    async with session.client("s3") as s3_client:  # type: ignore
        try:
            await s3_client.put_bucket_lifecycle_configuration(
                Bucket=bucket_name, LifecycleConfiguration=lifecycle_configuration
            )
            logger.info(
                f"Successfully applied lifecycle configuration from {config_path} to bucket '{bucket_name}'."
            )
        except ClientError as e:
            logger.error(
                f"Failed to apply lifecycle policy to bucket '{bucket_name}': {e}"
            )
            raise


async def upload_multiple_files_to_s3(
    file_mappings: list[tuple[str, str]],
    s3_config: S3Config,
    max_concurrent_uploads: int = 5,
) -> None:
    """
    异步批量上传多个文件到S3，支持并发控制。

    Args:
        file_mappings: 文件映射列表，每个元素为(本地路径, S3键名)的元组
        s3_config: S3配置对象
        max_concurrent_uploads: 最大并发上传数
    """
    semaphore = asyncio.Semaphore(max_concurrent_uploads)

    async def upload_with_semaphore(local_path: str, s3_key: str) -> None:
        async with semaphore:
            await upload_file_to_s3(local_path, s3_config, s3_key)

    tasks = [
        upload_with_semaphore(local_path, s3_key)
        for local_path, s3_key in file_mappings
    ]

    logger.info(
        f"Starting batch upload of {len(tasks)} files with max concurrency {max_concurrent_uploads}"
    )
    await asyncio.gather(*tasks, return_exceptions=True)
    logger.info("Batch upload completed")


class AsyncS3Client:
    """
    异步S3客户端，提供上下文管理器支持。
    """

    _CLIENT_NOT_INITIALIZED_ERROR = "Client not initialized. Use async context manager."

    def __init__(self, s3_config: S3Config):
        self.s3_config = s3_config
        self.session = aioboto3.Session()
        self.client: Any | None = None

    async def __aenter__(self) -> Self:
        # 构建 S3 客户端配置
        client_config = {
            "aws_access_key_id": self.s3_config.access_key,
            "aws_secret_access_key": self.s3_config.secret_key.get_secret_value(),
            "region_name": self.s3_config.region,
        }

        # 如果指定了自定义端点（如 MinIO），则添加端点配置
        if self.s3_config.endpoint_url:
            client_config["endpoint_url"] = self.s3_config.endpoint_url

        self.client = await self.session.client("s3", **client_config).__aenter__()
        return self

    async def __aexit__(
        self,
        exc_type: type[BaseException] | None,
        exc_val: BaseException | None,
        exc_tb: TracebackType | None,
    ) -> None:
        if self.client:
            await self.client.__aexit__(exc_type, exc_val, exc_tb)

    async def upload_file(self, local_path: str, s3_key: str) -> None:
        """上传文件到S3"""
        if self.client is None:
            raise RuntimeError(self._CLIENT_NOT_INITIALIZED_ERROR)
        config = TransferConfig(
            max_concurrency=self.s3_config.max_concurrency,
            max_bandwidth=self.s3_config.max_bandwidth_mbps * 1024 * 1024,
        )
        await self.client.upload_file(
            local_path,
            self.s3_config.bucket,
            s3_key,
            Config=config,
        )

    async def download_file(self, s3_key: str, local_path: str) -> None:
        """从S3下载文件"""
        if self.client is None:
            raise RuntimeError(self._CLIENT_NOT_INITIALIZED_ERROR)
        config = TransferConfig(
            max_concurrency=self.s3_config.max_concurrency,
            max_bandwidth=self.s3_config.max_bandwidth_mbps * 1024 * 1024,
        )
        await self.client.download_file(
            self.s3_config.bucket,
            s3_key,
            local_path,
            Config=config,
        )

    async def delete_object(self, s3_key: str) -> None:
        """删除S3对象"""
        if self.client is None:
            raise RuntimeError(self._CLIENT_NOT_INITIALIZED_ERROR)
        await self.client.delete_object(Bucket=self.s3_config.bucket, Key=s3_key)

    async def list_objects(self, prefix: str = "") -> AsyncGenerator[str]:
        """列出S3对象"""
        if self.client is None:
            raise RuntimeError(self._CLIENT_NOT_INITIALIZED_ERROR)
        paginator = self.client.get_paginator("list_objects_v2")
        async for page in paginator.paginate(
            Bucket=self.s3_config.bucket, Prefix=prefix
        ):
            if "Contents" in page:
                for obj in page["Contents"]:
                    yield obj["Key"]
