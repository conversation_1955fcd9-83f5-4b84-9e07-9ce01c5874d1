#!/usr/bin/env python3

"""
数据库连接器模块。

提供各种数据库的连接器实现，包括提取器和加载器。
"""

from .clickhouse import ClickHouseConnector, ClickHouseExtractor, ClickHouseLoader
from .mysql import MySQLConnector, MySQLExtractor, MySQLLoader
from .postgres import PostgresConnector, PostgresExtractor, PostgresLoader
from .query_builder import MongoQueryBuilder, QueryBuilder, SQLQueryBuilder

# 条件导入MongoDB连接器
try:
    from .mongo import MongoConnector, MongoExtractor, MongoLoader

    MONGO_AVAILABLE = True
except (ImportError, AttributeError):
    # MongoDB连接器不可用（motor库兼容性问题）
    MONGO_AVAILABLE = False
    MongoConnector = None  # type: ignore
    MongoExtractor = None  # type: ignore
    MongoLoader = None  # type: ignore

__all__ = [
    # MySQL 连接器
    "MySQLConnector",
    "MySQLExtractor",
    "MySQLLoader",
    # PostgreSQL 连接器
    "PostgresConnector",
    "PostgresExtractor",
    "PostgresLoader",
    # ClickHouse 连接器
    "ClickHouseConnector",
    "ClickHouseExtractor",
    "ClickHouseLoader",
    # MongoDB 连接器（可能不可用）
    "MongoConnector",
    "MongoExtractor",
    "MongoLoader",
    # 查询构造器
    "QueryBuilder",
    "MongoQueryBuilder",
    "SQLQueryBuilder",
]
