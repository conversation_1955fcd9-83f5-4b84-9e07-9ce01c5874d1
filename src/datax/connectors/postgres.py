#!/usr/bin/env python3

"""
PostgreSQL 连接器模块。

本模块提供了 PostgreSQL 数据库的提取器和加载器实现。
支持异步操作，使用 asyncpg 库进行高性能数据处理。
提供了完整的连接管理、查询构建和批量数据加载功能。

主要功能：
- PostgresConnector: 继承自 Connector 基类的连接管理类
- PostgresExtractor: 流式数据提取器，支持游标分批读取
- PostgresLoader: 批量数据加载器，支持 ON CONFLICT 冲突处理
- 支持自定义初始化 SQL 命令
- 支持 COPY FROM 高性能批量导入
"""

import logging
from collections.abc import AsyncIterator
from types import TracebackType
from typing import Any, Self
from uuid import UUID

import asyncpg  # type: ignore
import pandas as pd
from asyncpg import Connection

from ..core.loader import Loader
from ..core.metadata import CachedMetadataProvider, MetadataProvider
from ..models import (
    ColumnInfo,
    ColumnType,
    ConflictStrategy,
    ConstraintInfo,
    ConstraintType,
    DatasourceKind,
    ExportConfig,
    ImportConfig,
    IndexInfo,
    IndexType,
    PostgresConfig,
    QueryConfig,
    TableMetadata,
)
from .query_builder import QueryBuilder

logger = logging.getLogger(__name__)


_CONNECTION_NOT_ESTABLISHED = "PostgreSQL connection not established"


class PostgresConnector:
    """PostgreSQL 连接器。

    提供数据库连接管理功能，包括连接建立、配置和关闭。
    支持异步上下文管理器，确保连接的正确建立和释放。
    """

    def __init__(self, config: PostgresConfig):
        """
        初始化 PostgreSQL 连接器。

        参数:
            config: PostgreSQL 配置对象
        """
        self.config = config
        self.connection: Connection | None = None

    async def _open(self) -> None:
        """建立并配置 PostgreSQL 连接。"""
        if self.connection and not self.connection.is_closed():
            return

        try:
            # 构建连接参数
            db_params = {
                "database": self.config.database,
                "user": self.config.username,
                "password": self.config.get_password_value(),
                "host": self.config.host,
                "port": self.config.port,
            }
            db_params["server_settings"] = {
                "application_name": "datax-ETL",
                "client_encoding": "UTF8",
                "search_path": self.config.schema_name or "public",
            }

            self.connection = await asyncpg.connect(**db_params)

            # 如果有init_sql，在连接建立后执行
            if self.config.init_sql and self.connection:
                await self.connection.execute(self.config.init_sql)
                logger.info(f"Executed init_sql: {self.config.init_sql}")

            logger.debug("PostgreSQL connection established.")

        except asyncpg.PostgresError as e:
            logger.error(f"PostgreSQL connection failed: {e}")
            raise

    async def close(self) -> None:
        """关闭连接。"""
        if self.connection:
            try:
                await self.connection.close()
                self.connection = None
                logger.debug("PostgreSQL connection closed.")
            except asyncpg.PostgresError as e:
                logger.error(f"Error closing PostgreSQL connection: {e}")

    async def __aenter__(self) -> Self:
        """异步上下文管理器入口。"""
        await self._open()
        return self

    async def __aexit__(
        self,
        exc_type: type[BaseException] | None,
        exc_val: BaseException | None,
        exc_tb: TracebackType | None,
    ) -> None:
        """异步上下文管理器出口。"""
        await self.close()

    async def execute(self, query: str, params: tuple | None = None) -> Any:
        """执行SQL语句并返回状态信息。

        参数:
            query: SQL查询语句
            params: 查询参数

        返回:
            Any: 执行状态信息

        异常:
            ConnectionError: 连接未建立
        """
        if self.connection:
            return await self.connection.execute(query, params)
        else:
            raise ConnectionError(_CONNECTION_NOT_ESTABLISHED)

    async def fetch(self, query: str, params: tuple | None = None) -> Any:
        """执行查询并返回所有结果。

        参数:
            query: SQL查询语句
            params: 查询参数

        返回:
            Any: 查询结果列表

        异常:
            ConnectionError: 连接未建立
        """
        if self.connection:
            return await self.connection.fetch(query, params)
        else:
            raise ConnectionError(_CONNECTION_NOT_ESTABLISHED)

    async def fetchrow(self, query: str, params: tuple | None = None) -> Any:
        """执行查询并返回第一行结果。

        参数:
            query: SQL查询语句
            params: 查询参数

        返回:
            Any: 第一行结果，如果没有结果则返回None

        异常:
            ConnectionError: 连接未建立
        """
        if self.connection:
            return await self.connection.fetchrow(query, params)
        else:
            raise ConnectionError(_CONNECTION_NOT_ESTABLISHED)

    async def fetchval(self, query: str, params: tuple | None = None) -> Any:
        """执行查询并返回第一行第一列的值。

        参数:
            query: SQL查询语句
            params: 查询参数

        返回:
            Any: 第一行第一列的值

        异常:
            ConnectionError: 连接未建立
        """
        if self.connection:
            return await self.connection.fetchval(query, params)
        else:
            raise ConnectionError(_CONNECTION_NOT_ESTABLISHED)

    async def stream_query(
        self, query: str, params: tuple | None = None
    ) -> AsyncIterator[asyncpg.Record]:
        """执行查询并返回异步迭代器，逐行返回查询结果。

        该方法在内部管理数据库连接的事务上下文，使用服务器端游标进行流式处理。
        适合处理大规模数据集，避免将所有数据加载到内存中。

        参数:
            query: SQL查询语句
            params: 查询参数

        返回:
            AsyncIterator[asyncpg.Record]: 异步迭代器，逐行返回查询结果

        异常:
            ConnectionError: 连接未建立
        """
        if not self.connection:
            raise ConnectionError(_CONNECTION_NOT_ESTABLISHED)

        # 使用事务上下文管理器确保游标的正确创建和清理
        async with self.connection.transaction():
            async for record in self.connection.cursor(
                query, *params if params else ()
            ):
                yield record


class PostgresExtractor:
    """PostgreSQL 数据提取器。

    实现了PostgreSQL特定的数据提取逻辑。
    使用asyncpg进行流式数据处理，适合处理大规模数据集。
    支持灵活的查询构建，包括直接SQL查询、条件过滤、列选择等。
    """

    def __init__(self, config: PostgresConfig):
        """
        初始化 PostgreSQL 提取器。

        参数:
            config: PostgreSQL 配置对象
        """
        self._connector = PostgresConnector(config)
        self.query_builder = QueryBuilder(DatasourceKind.POSTGRES)

    async def __aenter__(self) -> Self:
        """异步上下文管理器入口。"""
        await self._connector.__aenter__()
        return self

    async def __aexit__(
        self,
        exc_type: type[BaseException] | None,
        exc_val: BaseException | None,
        exc_tb: TracebackType | None,
    ) -> None:
        """异步上下文管理器出口。"""
        await self._connector.__aexit__(exc_type, exc_val, exc_tb)

    def _process_record(self, record: asyncpg.Record) -> dict[str, Any]:
        """
        处理 asyncpg Record 为 dict。

        将 asyncpg 的 Record 对象转换为普通字典，并处理特殊的 PostgreSQL 数据类型。

        参数:
            record: asyncpg 查询结果记录

        返回:
            dict: 处理后的记录字典
        """
        processed = {}
        for key, value in record.items():
            # 检测 UUID 类型（包括 asyncpg 的 UUID 和标准 Python UUID）
            if value is not None:
                value_type = type(value)
                type_name = value_type.__name__

                # 检查是否为 UUID 类型（asyncpg 或标准 Python UUID）
                if type_name == "UUID" or isinstance(value, UUID):
                    # Convert UUID to string
                    processed[key] = str(value)
                else:
                    # Keep other types as-is
                    processed[key] = value
            else:
                # Keep None values as-is
                processed[key] = value  # type: ignore
        return processed

    def _build_query(self, query_config: QueryConfig) -> str:
        """
        根据 QueryConfig 模型构建 SQL 查询语句。

        参数:
            query_config: 查询配置对象

        返回:
            str: 构建的 SQL 查询语句
        """
        return self.query_builder.build_sql_query(query_config)

    async def extract_stream(
        self, query_config: QueryConfig, export_config: ExportConfig
    ) -> AsyncIterator[pd.DataFrame]:
        """使用服务器端游标进行流式提取。

        该方法现在使用 PostgresConnector 的 stream_query 方法进行流式数据提取，
        实现了关注点分离：连接器负责数据库连接和事务管理，提取器专注于数据处理和 DataFrame 转换。
        """
        query = self._build_query(query_config)
        logger.info(f"Executing query: {query}")

        buffer: list[dict[str, Any]] = []

        # 使用连接器的流式查询方法，连接器内部管理事务上下文
        async with self._connector:
            async for record in self._connector.stream_query(query):
                # 将 record 转换为 dict，并处理特殊类型
                row_dict = self._process_record(record)
                buffer.append(row_dict)

                if len(buffer) >= export_config.parquet_chunk_size:
                    logger.debug(f"Yielding PostgreSQL chunk with {len(buffer)} rows")
                    yield pd.DataFrame(buffer)
                    buffer.clear()

        # 输出剩余的数据
        if buffer:
            logger.debug(f"Yielding final PostgreSQL chunk with {len(buffer)} rows")
            yield pd.DataFrame(buffer)


class PostgresLoader(Loader):
    """PostgreSQL 数据加载器。

    实现了PostgreSQL特定的数据加载逻辑。
    支持批量数据加载和冲突处理策略（ON CONFLICT）。
    自动处理事务，确保数据一致性。
    """

    def __init__(self, config: PostgresConfig):
        """
        初始化 PostgreSQL 加载器。

        参数:
            config: PostgreSQL 配置对象
        """
        self._connector = PostgresConnector(config)

    async def __aenter__(self) -> Self:
        """异步上下文管理器入口。"""
        await self._connector.__aenter__()
        return self

    async def __aexit__(
        self,
        exc_type: type[BaseException] | None,
        exc_val: BaseException | None,
        exc_tb: TracebackType | None,
    ) -> None:
        """异步上下文管理器出口。"""
        await self._connector.__aexit__(exc_type, exc_val, exc_tb)

    async def load_chunk(self, data: pd.DataFrame, import_config: ImportConfig) -> None:
        """加载数据块到 PostgreSQL。"""
        if data.empty:
            return

        columns = ", ".join(f'"{col}"' for col in data.columns)
        placeholders = ", ".join(f"${i + 1}" for i in range(len(data.columns)))

        # 根据冲突策略选择SQL
        if import_config.conflict_strategy in (
            ConflictStrategy.UPSERT,
            ConflictStrategy.REPLACE,
        ):
            # 获取冲突目标列
            (
                conflict_target_columns,
                conflict_target_str,
            ) = await self._get_conflict_target_columns(import_config.table_name)

            if not conflict_target_columns:
                logger.warning(
                    f"No primary key or unique constraint found for table {import_config.table_name}. Using simple INSERT."
                )
                sql = f"INSERT INTO {import_config.table_name} ({columns}) VALUES ({placeholders})"
            else:
                sql = self._build_insert_sql(
                    import_config,
                    columns,
                    placeholders,
                    conflict_target_columns,
                    conflict_target_str,
                    list(data.columns),
                )
        else:
            sql = self._build_ignore_sql(
                import_config.table_name, columns, placeholders
            )

        await self._execute_load_operation(data, sql, import_config)

    async def _get_conflict_target_columns(
        self, table_name: str
    ) -> tuple[list[str], str]:
        """获取冲突目标列"""
        base_provider = PostgresMetadataProvider(self._connector.config)
        metadata_provider = CachedMetadataProvider(base_provider)

        try:
            table_metadata = await metadata_provider.get_table_metadata(table_name)
            conflict_target_columns = table_metadata.get_primary_key_columns()

            if not conflict_target_columns:
                unique_keys = table_metadata.get_unique_key_columns()
                if unique_keys:
                    conflict_target_columns = unique_keys[0]

            conflict_target_str = (
                ", ".join(f'"{col}"' for col in conflict_target_columns)
                if conflict_target_columns
                else ""
            )

            return conflict_target_columns, conflict_target_str

        except Exception as e:
            logger.warning(
                f"Failed to get table metadata for '{table_name}': {e}. Using ON CONFLICT DO NOTHING."
            )
            return [], ""

    def _build_insert_sql(
        self,
        import_config: ImportConfig,
        columns: str,
        placeholders: str,
        conflict_target_columns: list[str],
        conflict_target_str: str,
        data_columns: list[str],
    ) -> str:
        """构建插入SQL语句"""
        if (
            import_config.conflict_strategy == ConflictStrategy.UPSERT
            and conflict_target_columns
        ):
            return self._build_upsert_sql(
                import_config,
                columns,
                placeholders,
                conflict_target_columns,
                conflict_target_str,
                data_columns,
            )
        elif (
            import_config.conflict_strategy == ConflictStrategy.REPLACE
            and conflict_target_columns
        ):
            return self._build_replace_sql(
                import_config,
                columns,
                placeholders,
                conflict_target_columns,
                conflict_target_str,
                data_columns,
            )
        else:
            return self._build_ignore_sql(
                import_config.table_name, columns, placeholders
            )

    def _build_upsert_sql(
        self,
        import_config: ImportConfig,
        columns: str,
        placeholders: str,
        conflict_target_columns: list[str],
        conflict_target_str: str,
        data_columns: list[str],
    ) -> str:
        """构建UPSERT SQL语句"""
        update_cols = self._get_update_columns(data_columns, conflict_target_columns)

        if not update_cols:
            return self._build_do_nothing_sql(
                import_config.table_name, columns, placeholders, conflict_target_str
            )

        return f"""
            INSERT INTO {import_config.table_name} ({columns})
            VALUES ({placeholders})
            ON CONFLICT ({conflict_target_str}) DO UPDATE SET {update_cols}
        """

    def _build_replace_sql(
        self,
        import_config: ImportConfig,
        columns: str,
        placeholders: str,
        conflict_target_columns: list[str],
        conflict_target_str: str,
        data_columns: list[str],
    ) -> str:
        """构建REPLACE SQL语句"""
        update_cols = self._get_update_columns(data_columns, conflict_target_columns)

        if not update_cols:
            return self._build_do_nothing_sql(
                import_config.table_name, columns, placeholders, conflict_target_str
            )

        return f"""
            INSERT INTO {import_config.table_name} ({columns})
            VALUES ({placeholders})
            ON CONFLICT ({conflict_target_str}) DO UPDATE SET {update_cols}
        """

    def _build_ignore_sql(
        self, table_name: str, columns: str, placeholders: str
    ) -> str:
        """构建IGNORE SQL语句"""
        return f"""
            INSERT INTO {table_name} ({columns})
            VALUES ({placeholders})
            ON CONFLICT DO NOTHING
        """

    def _build_do_nothing_sql(
        self, table_name: str, columns: str, placeholders: str, conflict_target_str: str
    ) -> str:
        """构建DO NOTHING SQL语句"""
        return f"""
            INSERT INTO {table_name} ({columns})
            VALUES ({placeholders})
            ON CONFLICT ({conflict_target_str}) DO NOTHING
        """

    def _get_update_columns(
        self, data_columns: list[str], conflict_target_columns: list[str]
    ) -> str:
        """获取更新列字符串"""
        update_cols = [
            f'"{col}" = EXCLUDED."{col}"'
            for col in data_columns
            if col not in conflict_target_columns
        ]
        return ", ".join(update_cols)

    async def _execute_load_operation(
        self, data: pd.DataFrame, sql: str, import_config: ImportConfig
    ) -> None:
        """执行加载操作"""

        if self._connector.connection:
            records = [tuple(row) for row in data.values]
            await self._connector.connection.executemany(sql, records)

            logger.info(
                f"Loaded {len(data)} rows into '{import_config.table_name}' "
                f"using '{import_config.conflict_strategy}' strategy."
            )
        else:
            raise RuntimeError("Database connection is not established")


class PostgresMetadataProvider(MetadataProvider):
    """PostgreSQL 元数据查询提供者。

    实现了PostgreSQL特定的元数据查询逻辑。
    通过查询information_schema获取表结构、索引、约束等元数据信息。
    支持完整的表元数据提取，包括列信息、索引信息和约束信息。
    """

    def __init__(self, config: PostgresConfig):
        """
        初始化 PostgreSQL 元数据查询提供者。

        参数:
            config: PostgreSQL 配置对象
        """
        self._connector = PostgresConnector(config)

    def _map_postgres_type_to_column_type(self, pg_type: str) -> ColumnType:
        """将 PostgreSQL 数据类型映射到标准列类型"""
        pg_type = pg_type.lower()

        # 使用映射字典和辅助方法来降低复杂度
        numeric_type = self._map_numeric_type(pg_type)
        if numeric_type != ColumnType.UNKNOWN:
            return numeric_type

        string_type = self._map_string_type(pg_type)
        if string_type != ColumnType.UNKNOWN:
            return string_type

        datetime_type = self._map_datetime_type(pg_type)
        if datetime_type != ColumnType.UNKNOWN:
            return datetime_type

        other_type = self._map_other_type(pg_type)
        if other_type != ColumnType.UNKNOWN:
            return other_type

        return ColumnType.UNKNOWN

    def _map_numeric_type(self, pg_type: str) -> ColumnType:
        """映射数值类型"""
        numeric_mappings = {
            ("integer", "int", "int4"): ColumnType.INTEGER,
            ("bigint", "int8"): ColumnType.BIGINT,
            ("smallint", "int2"): ColumnType.SMALLINT,
            ("decimal", "numeric"): ColumnType.DECIMAL,
            ("real", "float4"): ColumnType.FLOAT,
            ("double precision", "float8"): ColumnType.DOUBLE,
        }

        for type_names, column_type in numeric_mappings.items():
            if pg_type in type_names:
                return column_type

        return ColumnType.UNKNOWN

    def _map_string_type(self, pg_type: str) -> ColumnType:
        """映射字符串类型"""
        if pg_type.startswith("character varying") or pg_type.startswith("varchar"):
            return ColumnType.VARCHAR
        elif pg_type.startswith("character") or pg_type.startswith("char"):
            return ColumnType.CHAR
        elif pg_type == "text":
            return ColumnType.TEXT

        return ColumnType.UNKNOWN

    def _map_datetime_type(self, pg_type: str) -> ColumnType:
        """映射日期时间类型"""
        if pg_type == "date":
            return ColumnType.DATE
        elif pg_type.startswith("time"):
            return ColumnType.TIME
        elif pg_type.startswith("timestamp"):
            return ColumnType.TIMESTAMP

        return ColumnType.UNKNOWN

    def _map_other_type(self, pg_type: str) -> ColumnType:
        """映射其他类型"""
        other_mappings = {
            "bytea": ColumnType.BINARY,
            ("boolean", "bool"): ColumnType.BOOLEAN,
            ("json", "jsonb"): ColumnType.JSON,
            "uuid": ColumnType.UUID,
        }

        for type_names, column_type in other_mappings.items():
            if isinstance(type_names, tuple):
                if pg_type in type_names:
                    return column_type
            else:
                if pg_type == type_names:
                    return column_type

        return ColumnType.UNKNOWN

    async def get_table_metadata(
        self, table_name: str, schema_name: str | None = None
    ) -> TableMetadata:
        """获取 PostgreSQL 表的元数据信息"""
        async with self._connector:
            # 使用默认schema如果未指定
            pg_config = self._connector.config
            schema = schema_name or pg_config.schema_name or "public"

            # 检查表是否存在
            if not await self.table_exists(table_name, schema):
                raise ValueError(f"Table '{schema}.{table_name}' does not exist")

            # 查询列信息
            columns = await self._get_column_info(table_name, schema)

            # 查询索引信息
            indexes = await self._get_index_info(table_name, schema)

            # 查询约束信息
            constraints = await self._get_constraint_info(table_name, schema)

            # 查询表注释
            table_comment = await self._get_table_comment(table_name, schema)

            return TableMetadata(
                table_name=table_name,
                schema_name=schema,
                database_name=self._connector.config.database,
                columns=columns,
                indexes=indexes,
                constraints=constraints,
                comment=table_comment,
            )

    async def _get_column_info(
        self, table_name: str, schema_name: str
    ) -> list[ColumnInfo]:
        """查询表的列信息"""
        query = """
            SELECT
                c.column_name,
                c.data_type,
                c.is_nullable,
                c.column_default,
                c.character_maximum_length,
                c.numeric_precision,
                c.numeric_scale,
                CASE WHEN pk.column_name IS NOT NULL THEN true ELSE false END as is_primary_key,
                CASE WHEN uk.column_name IS NOT NULL THEN true ELSE false END as is_unique,
                col_description(pgc.oid, c.ordinal_position) as column_comment
            FROM information_schema.columns c
            LEFT JOIN (
                SELECT ku.column_name
                FROM information_schema.table_constraints tc
                JOIN information_schema.key_column_usage ku
                    ON tc.constraint_name = ku.constraint_name
                    AND tc.table_schema = ku.table_schema
                    AND tc.table_name = ku.table_name
                WHERE tc.constraint_type = 'PRIMARY KEY'
                AND tc.table_schema = $2 AND tc.table_name = $1
            ) pk ON c.column_name = pk.column_name
            LEFT JOIN (
                SELECT ku.column_name
                FROM information_schema.table_constraints tc
                JOIN information_schema.key_column_usage ku
                    ON tc.constraint_name = ku.constraint_name
                    AND tc.table_schema = ku.table_schema
                    AND tc.table_name = ku.table_name
                WHERE tc.constraint_type = 'UNIQUE'
                AND tc.table_schema = $2 AND tc.table_name = $1
            ) uk ON c.column_name = uk.column_name
            LEFT JOIN pg_class pgc ON pgc.relname = c.table_name
            LEFT JOIN pg_namespace pgn ON pgn.oid = pgc.relnamespace AND pgn.nspname = c.table_schema
            WHERE c.table_schema = $2 AND c.table_name = $1
            ORDER BY c.ordinal_position
        """

        rows = await self._connector.fetch(query, (table_name, schema_name))

        columns = []
        for row in rows:
            is_auto_increment = bool(
                row["column_default"] and "nextval" in str(row["column_default"])
            )

            columns.append(
                ColumnInfo(
                    name=row["column_name"],
                    data_type=self._map_postgres_type_to_column_type(row["data_type"]),
                    is_nullable=row["is_nullable"] == "YES",
                    is_primary_key=bool(row["is_primary_key"]),
                    is_unique=bool(row["is_unique"] or row["is_primary_key"]),
                    is_auto_increment=is_auto_increment,
                    default_value=row["column_default"],
                    max_length=row["character_maximum_length"],
                    precision=row["numeric_precision"],
                    scale=row["numeric_scale"],
                    comment=row["column_comment"],
                )
            )

        return columns

    async def _get_index_info(
        self, table_name: str, schema_name: str
    ) -> list[IndexInfo]:
        """查询表的索引信息"""
        query = """
            SELECT
                i.indexname as index_name,
                i.indexdef,
                CASE WHEN c.contype = 'p' THEN true ELSE false END as is_primary,
                CASE WHEN c.contype = 'u' OR c.contype = 'p' THEN true ELSE false END as is_unique,
                obj_description(idx.oid) as comment
            FROM pg_indexes i
            LEFT JOIN pg_class t ON t.relname = i.tablename
            LEFT JOIN pg_namespace n ON n.nspname = i.schemaname AND n.oid = t.relnamespace
            LEFT JOIN pg_class idx ON idx.relname = i.indexname
            LEFT JOIN pg_constraint c ON c.conname = i.indexname AND c.conrelid = t.oid
            WHERE i.schemaname = $2 AND i.tablename = $1
            ORDER BY i.indexname
        """

        rows = await self._connector.fetch(query, (table_name, schema_name))

        indexes = []
        for row in rows:
            # 从indexdef中解析列名
            indexdef = row["indexdef"]
            columns = self._parse_index_columns_from_def(indexdef)

            is_primary = row["is_primary"]
            is_unique = row["is_unique"]

            if is_primary:
                index_type = IndexType.PRIMARY
            elif is_unique:
                index_type = IndexType.UNIQUE
            else:
                index_type = IndexType.INDEX

            indexes.append(
                IndexInfo(
                    name=row["index_name"],
                    index_type=index_type,
                    columns=columns,
                    is_unique=is_unique,
                    is_primary=is_primary,
                    comment=row["comment"],
                )
            )

        return indexes

    def _parse_index_columns_from_def(self, indexdef: str) -> list[str]:
        """从索引定义中解析列名"""
        # 简单的解析逻辑，提取括号内的列名
        import re

        match = re.search(r"\((.*?)\)", indexdef)
        if match:
            columns_str = match.group(1)
            # 分割列名并清理空格和引号
            columns = [col.strip().strip('"') for col in columns_str.split(",")]
            return columns
        return []

    async def _get_constraint_info(
        self, table_name: str, schema_name: str
    ) -> list[ConstraintInfo]:
        """查询表的约束信息"""
        query = """
            SELECT
                tc.constraint_name,
                tc.constraint_type,
                array_agg(kcu.column_name ORDER BY kcu.ordinal_position) as columns,
                ccu.table_name as referenced_table,
                array_agg(ccu.column_name ORDER BY kcu.ordinal_position) as referenced_columns,
                cc.check_clause
            FROM information_schema.table_constraints tc
            LEFT JOIN information_schema.key_column_usage kcu
                ON tc.constraint_name = kcu.constraint_name
                AND tc.table_schema = kcu.table_schema
                AND tc.table_name = kcu.table_name
            LEFT JOIN information_schema.constraint_column_usage ccu
                ON tc.constraint_name = ccu.constraint_name
                AND tc.table_schema = ccu.table_schema
            LEFT JOIN information_schema.check_constraints cc
                ON tc.constraint_name = cc.constraint_name
                AND tc.table_schema = cc.constraint_schema
            WHERE tc.table_schema = $2 AND tc.table_name = $1
            GROUP BY tc.constraint_name, tc.constraint_type, ccu.table_name, cc.check_clause
            ORDER BY tc.constraint_name
        """

        rows = await self._connector.fetch(query, (table_name, schema_name))

        constraints = []
        for row in rows:
            constraint_type_str = row["constraint_type"]

            if constraint_type_str == "PRIMARY KEY":
                constraint_type = ConstraintType.PRIMARY_KEY
            elif constraint_type_str == "UNIQUE":
                constraint_type = ConstraintType.UNIQUE
            elif constraint_type_str == "FOREIGN KEY":
                constraint_type = ConstraintType.FOREIGN_KEY
            elif constraint_type_str == "CHECK":
                constraint_type = ConstraintType.CHECK
            else:
                continue  # 跳过不支持的约束类型

            # 过滤掉 None 值
            columns = [col for col in (row["columns"] or []) if col is not None]
            referenced_columns = None
            if row["referenced_columns"]:
                referenced_columns = [
                    col for col in row["referenced_columns"] if col is not None
                ]

            constraints.append(
                ConstraintInfo(
                    name=row["constraint_name"],
                    constraint_type=constraint_type,
                    columns=columns,
                    referenced_table=row["referenced_table"],
                    referenced_columns=referenced_columns,
                    check_clause=row["check_clause"],
                )
            )

        return constraints

    async def _get_table_comment(self, table_name: str, schema_name: str) -> str | None:
        """查询表注释"""
        query = """
            SELECT obj_description(c.oid) as comment
            FROM pg_class c
            JOIN pg_namespace n ON n.oid = c.relnamespace
            WHERE n.nspname = $2 AND c.relname = $1
        """

        row = await self._connector.fetchrow(query, (table_name, schema_name))
        return row["comment"] if row and row["comment"] else None

    async def table_exists(
        self, table_name: str, schema_name: str | None = None
    ) -> bool:
        """检查表是否存在"""
        # 直接使用已存在的连接，避免嵌套异步上下文管理器
        pg_config = self._connector.config
        schema = schema_name or pg_config.schema_name or "public"

        query = """
            SELECT COUNT(*)
            FROM information_schema.tables
            WHERE table_schema = $2 AND table_name = $1
        """

        row = await self._connector.fetchrow(query, (table_name, schema))
        return row[0] > 0 if row else False

    async def get_database_name(self) -> str:
        """获取当前连接的数据库名称"""
        return self._connector.config.database
