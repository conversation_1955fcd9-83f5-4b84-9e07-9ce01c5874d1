#!/usr/bin/env python3

"""
MySQL 连接器模块。

本模块提供了 MySQL 数据库的提取器和加载器实现。
支持异步操作，使用服务器端游标进行流式数据处理，适合处理大规模数据集。
提供了完整的连接管理、查询构建和批量数据加载功能。

主要功能：
- MySQLConnector: 继承自 Connector 基类的连接管理类
- MySQLExtractor: 使用服务器端游标的流式数据提取器
- MySQLLoader: 批量数据加载器，支持冲突处理策略
- 支持自定义初始化 SQL 命令
- 支持 REPLACE INTO 和 INSERT IGNORE 策略
"""

import logging
from collections.abc import AsyncIterator
from types import TracebackType
from typing import Any, Self

import asyncmy  # type: ignore
import pandas as pd
from asyncmy import Connection
from asyncmy.cursors import Cursor  # type: ignore

from ..models import (
    ColumnInfo,
    ColumnType,
    ConflictStrategy,
    ConstraintInfo,
    ConstraintType,
    DatasourceKind,
    ExportConfig,
    ImportConfig,
    IndexInfo,
    IndexType,
    MySQLConfig,
    QueryConfig,
    TableMetadata,
)
from .query_builder import QueryBuilder

logger = logging.getLogger(__name__)


class MySQLConnector:
    """MySQL 连接器。

    提供数据库连接管理功能，包括连接建立、配置和关闭。
    支持自定义初始化SQL命令，在连接建立后自动执行。
    支持异步上下文管理器，确保连接的正确建立和释放。
    """

    def __init__(self, config: MySQLConfig):
        """
        初始化 MySQL 连接器。

        参数:
            config: MySQL 配置对象，包含主机、端口、用户名、密码等连接信息
        """
        self.config = config
        self.connection: Connection | None = None

    async def _open(self) -> None:
        """建立并配置 MySQL 连接。

        使用配置中的连接参数建立异步MySQL连接。
        如果配置了init_sql，会在连接建立后执行。
        如果连接已存在且有效，则直接返回。

        Raises:
            Exception: 当连接失败时抛出异常
        """
        if self.connection and self.connection.connected:
            return

        try:
            # 使用配置中的init_sql参数
            init_command = self.config.init_sql if self.config.init_sql else None

            self.connection = await asyncmy.connect(
                host=self.config.host,
                port=self.config.port,
                user=self.config.username,
                password=self.config.get_password_value(),
                database=self.config.database,
                charset="utf8mb4",
                init_command=init_command,
            )
            logger.debug("MySQL connection established.")

        except Exception as e:
            logger.error(f"MySQL connection failed: {e}")
            raise

    async def close(self) -> None:
        """关闭数据库连接。

        安全地关闭MySQL连接，即使在关闭过程中出现异常也会确保连接被标记为已关闭。
        如果连接不存在或已关闭，直接返回。
        """
        if self.connection:
            try:
                await self.connection.close()
            except Exception as e:
                logger.error(f"Error closing MySQL connection: {e}")
            finally:
                self.connection = None
                logger.debug("MySQL connection closed.")

    async def __aenter__(self) -> Self:
        """异步上下文管理器入口。"""
        await self._open()
        return self

    async def __aexit__(
        self,
        exc_type: type[BaseException] | None,
        exc_val: BaseException | None,
        exc_tb: TracebackType | None,
    ) -> None:
        """异步上下文管理器出口。"""
        await self.close()

    def cursor(self, cursor_type: Any | None = None) -> Cursor:
        """获取数据库游标对象。

        根据传入的游标类型创建游标对象，如果未指定类型则使用默认游标。

        参数:
            cursor_type: 游标类型，可以是 SSCursor 或 SSDictCursor

        返回:
            Cursor: 数据库游标对象
        """
        return (
            self.connection.cursor(cursor_type)  # type: ignore
            if cursor_type
            else self.connection.cursor()  # type: ignore
        )

    async def execute(self, sql_update: str, params: tuple | None = None) -> int:
        """执行SQL语句并返回受影响的行数。

        参数:
            query: SQL更新语句
            params: 查询参数

        返回:
            int: 受影响的行数

        异常:
            ConnectionError: 连接未建立
        """
        async with self.cursor() as cursor:
            try:
                await cursor.execute(sql_update, params)
                await self.connection.commit()  # type: ignore
                return int(cursor.rowcount)
            except Exception as e:
                await self.connection.rollback()  # type: ignore
                raise e

    async def executemany(self, sql_update: str, params: list[list]) -> int:
        """执行SQL语句并返回受影响的行数。

        参数:
            query: SQL更新语句
            params: 查询参数

        返回:
            int: 受影响的行数

        异常:
            ConnectionError: 连接未建立
        """
        async with self.cursor() as cursor:
            try:
                await cursor.executemany(sql_update, params)
                await self.connection.commit()  # type: ignore
                return int(cursor.rowcount)
            except Exception as e:
                await self.connection.rollback()  # type: ignore
                raise e

    async def fetchall(self, query: str, params: tuple | None = None) -> list[tuple]:
        """执行查询并返回所有结果。

        参数:
            query: SQL查询语句
            params: 查询参数

        返回:
            list[tuple]: 查询结果列表

        异常:
            ConnectionError: 连接未建立
        """
        async with self.cursor() as cursor:
            await cursor.execute(query, params)
            return await cursor.fetchall()  # type: ignore

    async def fetchone(self, query: str, params: tuple | None = None) -> tuple | None:
        """执行查询并返回第一行结果。

        参数:
            query: SQL查询语句
            params: 查询参数

        返回:
            tuple | None: 第一行结果，如果没有结果则返回None

        异常:
            ConnectionError: 连接未建立
        """
        async with self.cursor() as cursor:
            await cursor.execute(query, params)
            return await cursor.fetchone()  # type: ignore

    async def fetchmany(
        self, query: str, size: int, params: tuple | None = None
    ) -> list[tuple]:
        """执行查询并返回指定数量的结果。

        参数:
            query: SQL查询语句
            size: 要获取的行数
            params: 查询参数

        返回:
            list[tuple]: 查询结果列表

        异常:
            ConnectionError: 连接未建立
        """
        async with self.cursor() as cursor:
            await cursor.execute(query, params)
            return await cursor.fetchmany(size)  # type: ignore


class MySQLExtractor:
    """使用服务器端游标的 MySQL 数据提取器。

    继承自Extractor基类，实现了MySQL特定的数据提取逻辑。
    使用服务器端游标（SSDictCursor）进行流式数据处理，适合处理大规模数据集。
    支持灵活的查询构建，包括直接SQL查询、条件过滤、列选择等。
    """

    def __init__(self, config: MySQLConfig):
        """
        初始化 MySQL 提取器。

        参数:
            config: MySQL 配置对象，包含数据库连接所需的所有参数
        """
        self._connector = MySQLConnector(config)
        self.query_builder = QueryBuilder(DatasourceKind.MYSQL)

    def _build_query(self, query_config: QueryConfig) -> str:
        """
        根据 QueryConfig 模型构建 SQL 查询语句。

        使用通用查询构造器，支持四种优先级的查询构造：
        1. 直接SQL查询优先级最高
        2. 条件查询构造（sql_condition参数）
        3. 结果集限制（limit参数）
        4. 列选择（columns参数）

        参数:
            query_config: 查询配置对象

        返回:
            str: 构建的 SQL 查询语句
        """
        return self.query_builder.build_sql_query(query_config)

    async def extract_stream(
        self, query_config: QueryConfig, export_config: ExportConfig
    ) -> AsyncIterator[pd.DataFrame]:
        """
        使用服务器端游标进行流式数据提取。

        参数:
            query_config: 查询配置对象
            export_config: 导出配置对象

        Yields:
            pd.DataFrame: 数据块

        Raises:
              ConnectionError: 当数据库连接未建立时抛出
        """
        async with self._connector:
            # 使用服务器端游标进行流式处理
            async with self._connector.cursor() as cursor:
                query = self._build_query(query_config)
                logger.info(f"Executing query with server-side cursor: {query}")
                await cursor.execute(query)

                # 获取列名
                column_names = [desc[0] for desc in cursor.description]

                # 流式读取数据
                async for chunk_df in self._fetch_data_chunks(
                    cursor, column_names, export_config
                ):
                    if not chunk_df.empty:
                        logger.debug(
                            f"Yielding MySQL chunk with {len(chunk_df)} rows, columns: {list(chunk_df.columns)}"
                        )
                        yield chunk_df

    async def _fetch_data_chunks(
        self, cursor: Any, column_names: list[str], export_config: ExportConfig
    ) -> AsyncIterator[pd.DataFrame]:
        """从游标中批量获取数据并转换为DataFrame。

        将复杂的数据获取和转换逻辑抽取为独立方法，降低extract_stream的复杂度。
        使用缓冲区累积数据，达到指定大小后生成DataFrame。

        参数:
            cursor: 数据库游标对象
            column_names: 列名列表
            export_config: 导出配置，包含批次大小设置

        Yields:
            pd.DataFrame: 转换后的数据块
        """
        buffer: list[dict[str, Any]] = []

        while True:
            # 从服务器端游标获取一小批数据
            rows = await cursor.fetchmany(size=export_config.cursor_fetch_size)
            if not rows:
                break

            # 将行数据转换为字典格式
            dict_rows = self._convert_rows_to_dicts(rows, column_names)
            buffer.extend(dict_rows)

            # 当缓冲区达到指定大小时，生成DataFrame
            if len(buffer) >= export_config.parquet_chunk_size:
                yield pd.DataFrame(buffer)
                buffer.clear()

        # 处理剩余的数据
        if buffer:
            yield pd.DataFrame(buffer)

    def _convert_rows_to_dicts(
        self, rows: list[Any], column_names: list[str]
    ) -> list[dict[str, Any]]:
        """将数据库行转换为字典列表。

        处理不同格式的行数据，统一转换为字典格式。

        参数:
            rows: 从数据库获取的行数据
            column_names: 列名列表

        返回:
            list[dict[str, Any]]: 转换后的字典列表
        """
        dict_rows = []
        for row in rows:
            if isinstance(row, tuple):
                dict_row = dict(zip(column_names, row, strict=False))
                dict_rows.append(dict_row)
            else:
                dict_rows.append(row)
        return dict_rows


class MySQLLoader:
    """MySQL 数据加载器。

    继承自Loader基类，实现了MySQL特定的数据加载逻辑。
    支持批量数据加载和冲突处理策略（REPLACE和IGNORE）。
    自动处理事务，确保数据一致性。
    """

    def __init__(self, config: MySQLConfig):
        """
        初始化 MySQL 加载器。

        参数:
            config: MySQL 配置对象，包含数据库连接所需的所有参数
        """
        self._connector = MySQLConnector(config)

    async def load_chunk(self, data: pd.DataFrame, import_config: ImportConfig) -> None:
        """
        将 DataFrame 数据块加载到 MySQL。

        参数:
            data: 要加载的 DataFrame
            import_config: 导入配置对象

        Raises:
            Exception: 当数据加载失败时抛出
        """
        async with self._connector:
            if data.empty:
                return

            # 准备SQL语句和数据
            sql = self._build_insert_sql(data, import_config)
            records = [
                [None if pd.isna(val) else val for val in row] for row in data.values
            ]

            await self._execute_insert(sql, records, import_config.table_name)

    def _build_insert_sql(self, data: pd.DataFrame, import_config: ImportConfig) -> str:
        """构建INSERT SQL语句。

        根据冲突处理策略生成对应的SQL语句。
        支持REPLACE INTO和INSERT IGNORE两种策略。

        参数:
            data: 数据DataFrame，用于获取列名
            import_config: 导入配置，包含表名和冲突策略

        返回:
            str: 构建的SQL语句
        """
        # 使用DataFrame中实际存在的列
        cols = ", ".join(f"`{col}`" for col in data.columns)
        placeholders = ", ".join(["%s"] * len(data.columns))

        if import_config.conflict_strategy == ConflictStrategy.REPLACE:
            # REPLACE INTO是MySQL特有的扩展
            # 注意：这是DELETE然后INSERT的操作，可能触发ON DELETE触发器
            sql = f"REPLACE INTO `{import_config.table_name}` ({cols}) VALUES ({placeholders})"
        else:  # 'ignore'
            sql = f"INSERT IGNORE INTO `{import_config.table_name}` ({cols}) VALUES ({placeholders})"

        return sql

    async def _execute_insert(
        self, sql: str, records: list[list[Any]], table_name: str
    ) -> None:
        """执行批量插入操作。

        使用事务确保数据一致性，成功时提交，失败时回滚。
        记录插入的行数和使用的策略。

        参数:
            sql: INSERT SQL语句
            records: 要插入的数据记录
            table_name: 目标表名，用于日志记录

        Raises:
            Exception: 当插入失败时抛出
        """
        try:
            row_count = await self._connector.executemany(sql, records)
            # 提取策略类型用于日志
            strategy = "REPLACE" if "REPLACE" in sql else "IGNORE"
            logger.info(
                f"Loaded {row_count} rows into '{table_name}' using '{strategy}' strategy."
            )
        except Exception as e:
            logger.error(f"Error loading data into MySQL: {e}")
            raise


class MySQLMetadataProvider:
    """MySQL 元数据查询提供者。

    继承自MetadataProvider基类，实现了MySQL特定的元数据查询逻辑。
    通过查询INFORMATION_SCHEMA获取表结构、索引、约束等元数据信息。
    支持完整的表元数据提取，包括列信息、索引信息和约束信息。
    """

    def __init__(self, config: MySQLConfig):
        """
        初始化 MySQL 元数据查询提供者。

        参数:
            config: MySQL 配置对象，包含数据库连接所需的所有参数
        """
        self._connector = MySQLConnector(config)

    def _map_mysql_type_to_column_type(self, mysql_type: str) -> ColumnType:
        """将 MySQL 数据类型映射到标准列类型。

        提供MySQL数据类型到统一列类型枚举的映射。
        支持所有常见的MySQL数据类型。

        参数:
            mysql_type: MySQL的数据类型字符串

        返回:
            ColumnType: 对应的标准列类型枚举值
        """
        mysql_type = mysql_type.lower()

        # 数值类型映射
        numeric_mappings = {
            "int": ColumnType.INTEGER,
            "bigint": ColumnType.BIGINT,
            "smallint": ColumnType.SMALLINT,
            "tinyint": ColumnType.TINYINT,
            "decimal": ColumnType.DECIMAL,
            "numeric": ColumnType.DECIMAL,
            "float": ColumnType.FLOAT,
            "double": ColumnType.DOUBLE,
        }

        # 字符串类型映射
        string_mappings = {
            "varchar": ColumnType.VARCHAR,
            "char": ColumnType.CHAR,
            "text": ColumnType.TEXT,
            "longtext": ColumnType.LONGTEXT,
            "mediumtext": ColumnType.MEDIUMTEXT,
        }

        # 日期时间类型映射
        datetime_mappings = {
            "date": ColumnType.DATE,
            "time": ColumnType.TIME,
            "datetime": ColumnType.DATETIME,
            "timestamp": ColumnType.TIMESTAMP,
            "year": ColumnType.YEAR,
        }

        # 二进制类型映射
        binary_mappings = {
            "binary": ColumnType.BINARY,
            "varbinary": ColumnType.VARBINARY,
            "blob": ColumnType.BLOB,
            "longblob": ColumnType.LONGBLOB,
        }

        # 布尔类型映射
        boolean_mappings = {
            "bool": ColumnType.BOOLEAN,
            "boolean": ColumnType.BOOLEAN,
        }

        # 其他类型映射
        other_mappings = {
            "json": ColumnType.JSON,
            "enum": ColumnType.ENUM,
            "set": ColumnType.SET,
        }

        # 合并所有映射
        all_mappings = {
            **numeric_mappings,
            **string_mappings,
            **datetime_mappings,
            **binary_mappings,
            **boolean_mappings,
            **other_mappings,
        }

        # 精确匹配优先，然后进行前缀匹配
        # 首先尝试精确匹配
        if mysql_type in all_mappings:
            return all_mappings[mysql_type]

        # 对于需要前缀匹配的类型，按优先级排序处理
        # 数值类型通常需要前缀匹配（如 int(11), bigint(20) 等）
        for type_key, column_type in numeric_mappings.items():
            if mysql_type.startswith(type_key):
                return column_type

        # 字符串类型通常需要前缀匹配（如 varchar(255), char(10) 等）
        for type_key, column_type in string_mappings.items():
            if mysql_type.startswith(type_key):
                return column_type

        # 二进制类型通常需要前缀匹配（如 binary(16), varbinary(255) 等）
        for type_key, column_type in binary_mappings.items():
            if mysql_type.startswith(type_key):
                return column_type

        # 日期时间类型需要精确匹配，避免 datetime 被错误映射为 date
        for type_key, column_type in datetime_mappings.items():
            if mysql_type == type_key:
                return column_type

        # 其他类型也需要精确匹配
        for type_key, column_type in {**boolean_mappings, **other_mappings}.items():
            if mysql_type == type_key:
                return column_type

        return ColumnType.UNKNOWN

    async def get_table_metadata(
        self, table_name: str, schema_name: str | None = None
    ) -> TableMetadata:
        """获取 MySQL 表的元数据信息。

        查询表的完整元数据，包括列信息、索引信息、约束信息和表注释。
        MySQL使用数据库名而不是模式名，所以忽略schema_name参数。

        参数:
            table_name: 表名
            schema_name: 模式名（MySQL中忽略此参数）

        返回:
            TableMetadata: 包含表的完整元数据信息

        Raises:
            RuntimeError: 当数据库连接失败时抛出
            ValueError: 当表不存在时抛出
        """
        async with self._connector:
            # 检查表是否存在
            if not await self.table_exists(table_name, schema_name):
                raise ValueError(
                    f"Table '{table_name}' does not exist in database '{self._connector.config.database}'"
                )

            # 查询各种元数据
            columns = await self._get_column_info(table_name)
            indexes = await self._get_index_info(table_name)
            constraints = await self._get_constraint_info(table_name)
            table_comment = await self._get_table_comment(table_name)

            return TableMetadata(
                table_name=table_name,
                database_name=self._connector.config.database,
                columns=columns,
                indexes=indexes,
                constraints=constraints,
                comment=table_comment,
            )

    async def _get_column_info(self, table_name: str) -> list[ColumnInfo]:
        """查询表的列信息。

        从INFORMATION_SCHEMA.COLUMNS获取列的详细信息。
        包括列名、数据类型、是否可空、默认值、注释等。

        参数:
            table_name: 表名

        返回:
            list[ColumnInfo]: 列信息列表

        Raises:
            RuntimeError: 当没有数据库连接时抛出
        """
        query = """
            SELECT
                COLUMN_NAME,
                DATA_TYPE,
                IS_NULLABLE,
                COLUMN_DEFAULT,
                CHARACTER_MAXIMUM_LENGTH,
                NUMERIC_PRECISION,
                NUMERIC_SCALE,
                COLUMN_KEY,
                EXTRA,
                COLUMN_COMMENT
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = %s
            ORDER BY ORDINAL_POSITION
        """

        rows = await self._connector.fetchall(query, (table_name,))

        columns = []
        for row in rows:
            column_info = self._parse_column_row(row)
            columns.append(column_info)

        return columns

    def _parse_column_row(self, row: tuple[Any, ...]) -> ColumnInfo:
        """解析列信息行数据。

        将从INFORMATION_SCHEMA查询的行数据转换为ColumnInfo对象。

        参数:
            row: 包含列信息的元组

        返回:
            ColumnInfo: 解析后的列信息对象
        """
        (
            column_name,
            data_type,
            is_nullable,
            default_value,
            max_length,
            precision,
            scale,
            column_key,
            extra,
            comment,
        ) = row

        return ColumnInfo(
            name=column_name,
            data_type=self._map_mysql_type_to_column_type(data_type),
            is_nullable=is_nullable == "YES",
            is_primary_key=column_key == "PRI",
            is_unique=column_key in ("PRI", "UNI"),
            is_auto_increment=("auto_increment" in extra.lower() if extra else False),
            default_value=default_value,
            max_length=max_length,
            precision=precision,
            scale=scale,
            comment=comment,
        )

    async def _get_index_info(self, table_name: str) -> list[IndexInfo]:
        """查询表的索引信息。

        从INFORMATION_SCHEMA.STATISTICS获取索引的详细信息。
        将多行索引数据按索引名分组，构建完整的索引信息。

        参数:
            table_name: 表名

        返回:
            list[IndexInfo]: 索引信息列表

        Raises:
            RuntimeError: 当没有数据库连接时抛出
        """
        query = """
            SELECT
                INDEX_NAME,
                NON_UNIQUE,
                COLUMN_NAME,
                INDEX_COMMENT
            FROM INFORMATION_SCHEMA.STATISTICS
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = %s
            ORDER BY INDEX_NAME, SEQ_IN_INDEX
        """

        rows = await self._connector.fetchall(query, (table_name,))

        # 按索引名分组
        index_groups = self._group_index_rows(rows)

        # 构建索引信息列表
        return self._build_index_info_list(index_groups)

    def _group_index_rows(
        self, rows: list[tuple[Any, ...]]
    ) -> dict[str, list[tuple[Any, ...]]]:
        """将索引行按索引名分组。

        参数:
            rows: 索引查询结果行

        返回:
            dict[str, list[tuple]]: 按索引名分组的结果
        """
        index_groups: dict[str, list[tuple[Any, ...]]] = {}
        for row in rows:
            index_name = row[0]
            if index_name not in index_groups:
                index_groups[index_name] = []
            index_groups[index_name].append(row)
        return index_groups

    def _build_index_info_list(
        self, index_groups: dict[str, list[tuple[Any, ...]]]
    ) -> list[IndexInfo]:
        """构建索引信息列表。

        参数:
            index_groups: 按索引名分组的数据

        返回:
            list[IndexInfo]: 索引信息列表
        """
        indexes = []
        for index_name, group in index_groups.items():
            non_unique, _, comment = group[0][1:4]  # 取第一行的信息
            columns = [row[2] for row in group]  # 所有列名

            is_unique = non_unique == 0
            is_primary = index_name == "PRIMARY"

            if is_primary:
                index_type = IndexType.PRIMARY
            elif is_unique:
                index_type = IndexType.UNIQUE
            else:
                index_type = IndexType.INDEX

            indexes.append(
                IndexInfo(
                    name=index_name,
                    index_type=index_type,
                    columns=columns,
                    is_unique=is_unique,
                    is_primary=is_primary,
                    comment=comment,
                )
            )

        return indexes

    async def _get_constraint_info(self, table_name: str) -> list[ConstraintInfo]:
        """查询表的约束信息。

        查询主键、唯一约束和外键约束的详细信息。
        分别处理不同类型的约束，构建完整的约束信息列表。

        参数:
            table_name: 表名

        返回:
            list[ConstraintInfo]: 约束信息列表

        Raises:
            RuntimeError: 当没有数据库连接时抛出
        """
        constraints = []

        # 查询并添加主键约束
        pk_constraint = await self._get_primary_key_constraint(table_name)
        if pk_constraint:
            constraints.append(pk_constraint)

        # 查询并添加唯一约束
        unique_constraints = await self._get_unique_constraints(table_name)
        constraints.extend(unique_constraints)

        # 查询并添加外键约束
        fk_constraints = await self._get_foreign_key_constraints(table_name)
        constraints.extend(fk_constraints)

        return constraints

    async def _get_primary_key_constraint(
        self, table_name: str
    ) -> ConstraintInfo | None:
        """查询主键约束信息。

        参数:
            table_name: 表名

        返回:
            ConstraintInfo | None: 主键约束信息，如果没有主键则返回None
        """
        pk_query = """
            SELECT
                CONSTRAINT_NAME,
                COLUMN_NAME
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = %s
            AND CONSTRAINT_NAME = 'PRIMARY'
            ORDER BY ORDINAL_POSITION
        """

        pk_rows = await self._connector.fetchall(pk_query, (table_name,))
        if pk_rows:
            pk_columns = [row[1] for row in pk_rows]
            return ConstraintInfo(
                name="PRIMARY",
                constraint_type=ConstraintType.PRIMARY_KEY,
                columns=pk_columns,
            )
        return None

    async def _get_unique_constraints(self, table_name: str) -> list[ConstraintInfo]:
        """查询唯一约束信息。

        参数:
            table_name: 表名

        返回:
            list[ConstraintInfo]: 唯一约束信息列表
        """
        unique_query = """
            SELECT
                tc.CONSTRAINT_NAME,
                kcu.COLUMN_NAME
            FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
            JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
                ON tc.CONSTRAINT_NAME = kcu.CONSTRAINT_NAME
                AND tc.TABLE_SCHEMA = kcu.TABLE_SCHEMA
                AND tc.TABLE_NAME = kcu.TABLE_NAME
            WHERE tc.TABLE_SCHEMA = DATABASE() AND tc.TABLE_NAME = %s
            AND tc.CONSTRAINT_TYPE = 'UNIQUE'
            ORDER BY tc.CONSTRAINT_NAME, kcu.ORDINAL_POSITION
        """

        unique_rows = await self._connector.fetchall(unique_query, (table_name,))

        # 按约束名分组
        unique_groups: dict[str, list[str]] = {}
        for constraint_name, column_name in unique_rows:
            if constraint_name not in unique_groups:
                unique_groups[constraint_name] = []
            unique_groups[constraint_name].append(column_name)

        # 构建约束信息列表
        constraints = []
        for constraint_name, columns in unique_groups.items():
            constraints.append(
                ConstraintInfo(
                    name=constraint_name,
                    constraint_type=ConstraintType.UNIQUE,
                    columns=columns,
                )
            )

        return constraints

    async def _get_foreign_key_constraints(
        self, table_name: str
    ) -> list[ConstraintInfo]:
        """查询外键约束信息。

        参数:
            table_name: 表名

        返回:
            list[ConstraintInfo]: 外键约束信息列表
        """
        fk_query = """
            SELECT
                kcu.CONSTRAINT_NAME,
                kcu.COLUMN_NAME,
                kcu.REFERENCED_TABLE_NAME,
                kcu.REFERENCED_COLUMN_NAME
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
            JOIN INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
                ON kcu.CONSTRAINT_NAME = tc.CONSTRAINT_NAME
                AND kcu.TABLE_SCHEMA = tc.TABLE_SCHEMA
                AND kcu.TABLE_NAME = tc.TABLE_NAME
            WHERE kcu.TABLE_SCHEMA = DATABASE() AND kcu.TABLE_NAME = %s
            AND tc.CONSTRAINT_TYPE = 'FOREIGN KEY'
            ORDER BY kcu.CONSTRAINT_NAME, kcu.ORDINAL_POSITION
        """

        fk_rows = await self._connector.fetchall(fk_query, (table_name,))

        # 按约束名分组并解析
        fk_groups: dict[str, tuple[list[str], str, list[str]]] = {}
        for constraint_name, column_name, ref_table, ref_column in fk_rows:
            if constraint_name not in fk_groups:
                fk_groups[constraint_name] = ([], ref_table, [])
            fk_groups[constraint_name][0].append(column_name)
            fk_groups[constraint_name][2].append(ref_column)

        # 构建约束信息列表
        constraints = []
        for constraint_name, (columns, ref_table, ref_columns) in fk_groups.items():
            constraints.append(
                ConstraintInfo(
                    name=constraint_name,
                    constraint_type=ConstraintType.FOREIGN_KEY,
                    columns=columns,
                    referenced_table=ref_table,
                    referenced_columns=ref_columns,
                )
            )

        return constraints

    async def _get_table_comment(self, table_name: str) -> str | None:
        """查询表注释。

        从INFORMATION_SCHEMA.TABLES获取表的注释信息。

        参数:
            table_name: 表名

        返回:
            str | None: 表注释，如果没有注释则返回None

        Raises:
            RuntimeError: 当没有数据库连接时抛出
        """
        query = """
            SELECT TABLE_COMMENT
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = %s
        """
        row = await self._connector.fetchone(query, (table_name,))
        return row[0] if row and row[0] else None

    async def table_exists(
        self, table_name: str, schema_name: str | None = None
    ) -> bool:
        """检查表是否存在。

        查询INFORMATION_SCHEMA.TABLES确认表是否存在。
        MySQL使用数据库名而不是模式名，所以忽略schema_name参数。

        参数:
            table_name: 表名
            schema_name: 模式名（MySQL中忽略此参数）

        返回:
            bool: 表存在返回True，否则返回False
        """
        # MySQL 使用数据库名而不是模式名，所以忽略 schema_name 参数
        _ = schema_name  # 避免未使用参数警告

        # 直接使用已存在的连接，避免嵌套异步上下文管理器
        query = """
            SELECT COUNT(*)
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = %s
        """

        row = await self._connector.fetchone(query, (table_name,))
        return row[0] > 0 if row else False
