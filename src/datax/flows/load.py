#!/usr/bin/env python3

"""
数据导入流程模块。

本模块使用 Prefect 工作流框架实现数据导入功能。
提供了从 S3 下载 Parquet 文件、转换数据、加载到数据库的完整流程。

主要功能：
- download_from_s3: 从 S3 下载 Parquet 文件
- load_parquet_to_db: 读取 Parquet 文件并加载到数据库
- import_flow: 统一的数据库导入工作流
"""

import logging
import os
import tempfile
from collections.abc import AsyncGenerator
from pathlib import Path

import pandas as pd
import pyarrow.parquet as pq
from prefect import flow, task

from ..core.factories import get_loader, get_translator
from ..models.config import ImportConfig
from ..utils.s3 import AsyncS3Client

logger = logging.getLogger(__name__)


@task
async def download_from_s3(import_config: ImportConfig, output_dir: str) -> list[str]:
    """
    从 S3 下载 Parquet 文件到本地目录。

    参数:
        import_config: 导入配置对象
        output_dir: 本地输出目录

    返回:
        list[str]: 下载的本地文件路径列表
    """
    downloaded_files = []

    async with AsyncS3Client(import_config.s3_source) as s3_client:
        # 列出 S3 中的所有 Parquet 文件
        async for s3_key in s3_client.list_objects(prefix=""):
            if s3_key.endswith(".parquet"):
                local_filename = Path(s3_key).name
                local_path = os.path.join(output_dir, local_filename)

                logger.info(f"Downloading {s3_key} to {local_path}")
                await s3_client.download_file(s3_key, local_path)
                downloaded_files.append(local_path)

    logger.info(f"Downloaded {len(downloaded_files)} Parquet files")
    return downloaded_files


@task
async def load_parquet_to_db(
    parquet_files: list[str], import_config: ImportConfig
) -> None:
    """
    读取 Parquet 文件并加载到数据库。

    参数:
        parquet_files: Parquet 文件路径列表
        import_config: 导入配置对象
    """
    if not parquet_files:
        logger.warning("No Parquet files to load")
        return

    loader = get_loader(import_config.target_db)
    translator = get_translator(import_config.translators)

    total_rows_loaded = 0

    try:
        async with loader:
            # 处理第一个文件以准备目标表
            first_file = parquet_files[0]
            first_table = pq.read_table(first_file)
            await loader.prepare_target(first_table.schema, import_config.table_name)

            # 处理所有文件
            for parquet_file in parquet_files:
                logger.info(f"Processing file: {parquet_file}")

                # 读取 Parquet 文件
                table = pq.read_table(parquet_file)
                df = table.to_pandas()

                if df.empty:
                    logger.warning(f"Empty DataFrame from {parquet_file}, skipping")
                    continue

                # 应用数据转换
                async for transformed_df in translator.transform_stream(
                    _async_dataframe_generator([df])
                ):
                    if not transformed_df.empty:
                        await loader.load_chunk(transformed_df, import_config)
                        total_rows_loaded += len(transformed_df)
                        logger.info(
                            f"Loaded {len(transformed_df)} rows from {parquet_file}"
                        )

    except Exception as e:
        logger.error(f"Error during data loading: {e}")
        raise

    logger.info(f"Successfully loaded {total_rows_loaded} total rows")


async def _async_dataframe_generator(
    dataframes: list[pd.DataFrame],
) -> AsyncGenerator[pd.DataFrame, None]:
    """
    将 DataFrame 列表转换为异步生成器。

    参数:
        dataframes: DataFrame 列表

    产出:
        pd.DataFrame: 单个 DataFrame
    """
    for df in dataframes:
        yield df


@flow(name="Unified Database Import Flow", log_prints=True)
async def import_flow(config: ImportConfig):
    """
    一个统一的数据库导入工作流。

    从 S3 下载 Parquet 文件，进行转换，并加载到目标数据库。

    参数:
        config: 导入配置对象，包含 S3 源、目标数据库等配置
    """
    # 使用临时目录来存放下载的 Parquet 文件
    with tempfile.TemporaryDirectory() as tmpdir:
        print(f"临时文件将存储在: {tmpdir}")

        # 从 S3 下载 Parquet 文件
        local_files = await download_from_s3(config, tmpdir)

        if not local_files:
            print("没有找到 Parquet 文件，流程结束。")
            return

        print(f"成功下载 {len(local_files)} 个 Parquet 文件，准备加载...")

        # 加载数据到数据库
        await load_parquet_to_db(local_files, config)

    print("导入流程执行完毕。")


if __name__ == "__main__":
    # 示例: 如何在本地运行这个 Flow
    import asyncio
    from pydantic import SecretStr
    from ..models.config import PostgresConfig, S3Config

    # 注意：运行此示例需要一个本地运行的 PostgreSQL 实例和 MinIO/S3
    pg_config = PostgresConfig(
        username="testuser",
        password=SecretStr("testpass"),
        host="localhost",
        port=5432,
        database="testdb",
        schema_name="public",
    )

    s3_config = S3Config(
        bucket="test-bucket",
        access_key="test-access-key",
        secret_key=SecretStr("test-secret-key"),
    )

    import_config = ImportConfig(
        table_name="sample_data",
        s3_source=s3_config,
        target_db=pg_config,
        conflict_strategy="ignore",
    )

    asyncio.run(import_flow(import_config))
