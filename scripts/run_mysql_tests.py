#!/usr/bin/env python3
"""
MySQL 集成测试运行脚本

提供便捷的命令行接口来运行各种MySQL测试场景。
支持运行特定测试类、单个测试方法，或完整的测试套件。
"""

import argparse
import subprocess
import sys
from pathlib import Path


def run_command(cmd: list[str], description: str) -> int:
    """
    运行命令并显示结果

    参数:
        cmd: 要执行的命令列表
        description: 命令描述

    返回:
        int: 命令执行返回码
    """
    print(f"\n🔄 {description}")
    print(f"执行命令: {' '.join(cmd)}")
    print("-" * 60)

    try:
        result = subprocess.run(cmd, check=False, cwd=Path(__file__).parent.parent)
        if result.returncode == 0:
            print(f"✅ {description} - 成功")
        else:
            print(f"❌ {description} - 失败 (返回码: {result.returncode})")
        return result.returncode
    except Exception as e:
        print(f"❌ {description} - 执行错误: {e}")
        return 1


def main() -> int:
    """主函数"""
    parser = argparse.ArgumentParser(
        description="MySQL 集成测试运行工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  %(prog)s --all              # 运行所有MySQL测试
  %(prog)s --basic            # 运行基础功能测试
  %(prog)s --datatypes        # 运行数据类型测试
  %(prog)s --batch            # 运行分批导出测试
  %(prog)s --conflict         # 运行冲突处理测试
  %(prog)s --method connection # 运行特定测试方法
  %(prog)s --verbose          # 显示详细输出
        """,
    )

    # 测试选择选项
    test_group = parser.add_mutually_exclusive_group()
    test_group.add_argument("--all", action="store_true", help="运行所有MySQL集成测试")
    test_group.add_argument(
        "--basic", action="store_true", help="运行基础功能测试 (连接、导入导出、元数据)"
    )
    test_group.add_argument(
        "--datatypes", action="store_true", help="运行数据类型兼容性测试"
    )
    test_group.add_argument("--batch", action="store_true", help="运行分批导出专项测试")
    test_group.add_argument(
        "--conflict", action="store_true", help="运行主键冲突处理专项测试"
    )
    test_group.add_argument(
        "--method",
        type=str,
        metavar="METHOD_NAME",
        help="运行特定的测试方法 (支持部分匹配)",
    )

    # 输出选项
    parser.add_argument("--verbose", "-v", action="store_true", help="显示详细输出")
    parser.add_argument("--debug", action="store_true", help="显示调试信息")
    parser.add_argument(
        "--no-cleanup", action="store_true", help="保留测试数据（用于调试）"
    )

    args = parser.parse_args()

    # 如果没有指定任何测试，默认运行所有测试
    if not any(
        [
            args.all,
            args.basic,
            args.datatypes,
            args.batch,
            args.conflict,
            args.method,
        ]
    ):
        args.all = True

    # 基础pytest命令
    base_cmd = ["pytest"]
    test_file = "tests/integration/test_mysql_import_export.py"

    # 添加输出选项
    if args.verbose:
        base_cmd.extend(["-v", "-s"])
    if args.debug:
        base_cmd.extend(["--log-level=DEBUG"])

    # 构建测试命令
    commands = []

    if args.all:
        cmd = base_cmd + [test_file]
        commands.append((cmd, "运行所有MySQL集成测试"))

    elif args.basic:
        cmd = base_cmd + [f"{test_file}::TestMySQLBasicOperations"]
        commands.append((cmd, "运行基础功能测试"))

    elif args.datatypes:
        cmd = base_cmd + [f"{test_file}::TestMySQLDataTypes"]
        commands.append((cmd, "运行数据类型兼容性测试"))

    elif args.batch:
        cmd = base_cmd + [f"{test_file}::TestMySQLBatchExport"]
        commands.append((cmd, "运行分批导出测试"))

    elif args.conflict:
        cmd = base_cmd + [f"{test_file}::TestMySQLConflictHandling"]
        commands.append((cmd, "运行冲突处理测试"))

    elif args.method:
        # 支持部分匹配测试方法名
        method_name = args.method
        if "connection" in method_name.lower():
            cmd = base_cmd + [
                f"{test_file}::TestMySQLBasicOperations::test_mysql_connection"
            ]
            commands.append((cmd, "运行连接测试"))
        elif "basic" in method_name.lower() or "extract" in method_name.lower():
            cmd = base_cmd + [
                f"{test_file}::TestMySQLBasicOperations::test_basic_extract_and_load"
            ]
            commands.append((cmd, "运行基础导入导出测试"))
        elif "metadata" in method_name.lower():
            cmd = base_cmd + [
                f"{test_file}::TestMySQLBasicOperations::test_metadata_provider"
            ]
            commands.append((cmd, "运行元数据测试"))
        elif "numeric" in method_name.lower():
            cmd = base_cmd + [
                f"{test_file}::TestMySQLDataTypes::test_numeric_types_extract_load"
            ]
            commands.append((cmd, "运行数值类型测试"))
        elif "string" in method_name.lower():
            cmd = base_cmd + [
                f"{test_file}::TestMySQLDataTypes::test_string_types_extract_load"
            ]
            commands.append((cmd, "运行字符串类型测试"))
        elif "ignore" in method_name.lower():
            cmd = base_cmd + [
                f"{test_file}::TestMySQLConflictHandling::test_ignore_strategy"
            ]
            commands.append((cmd, "运行IGNORE策略测试"))
        elif "replace" in method_name.lower():
            cmd = base_cmd + [
                f"{test_file}::TestMySQLConflictHandling::test_replace_strategy"
            ]
            commands.append((cmd, "运行REPLACE策略测试"))
        else:
            # 使用-k参数进行模糊匹配
            cmd = base_cmd + [test_file, "-k", method_name]
            commands.append((cmd, f"运行包含 '{method_name}' 的测试"))

    # 执行命令
    print("🚀 开始运行MySQL集成测试")
    print("=" * 60)

    total_commands = len(commands)
    failed_commands = 0

    for i, (cmd, description) in enumerate(commands, 1):
        print(f"\n📋 步骤 {i}/{total_commands}")
        return_code = run_command(cmd, description)
        if return_code != 0:
            failed_commands += 1

    # 总结结果
    print("\n" + "=" * 60)
    if failed_commands == 0:
        print("🎉 所有测试执行成功！")
        return 0
    else:
        print(f"⚠️  共有 {failed_commands}/{total_commands} 个测试失败")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
